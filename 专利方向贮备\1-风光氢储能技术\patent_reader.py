#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专利文档完整内容读取器
用于读取CN109980677A.pdf的完整内容
"""

import fitz  # PyMuPDF
import os
import sys
from pathlib import Path

def read_patent_pdf():
    """读取专利PDF的完整内容"""
    
    print("🔍 专利文档完整内容读取器")
    print("=" * 60)
    
    # 目标专利文件
    patent_file = "03-中国专利/CN109980677A.pdf"
    
    # 检查文件是否存在
    if not os.path.exists(patent_file):
        print(f"❌ 错误：文件 {patent_file} 不存在！")
        return False
    
    # 获取文件大小
    file_size = os.path.getsize(patent_file)
    print(f"📄 目标文件: {patent_file}")
    print(f"📏 文件大小: {file_size / 1024 / 1024:.2f} MB")
    print()
    
    try:
        print(f"🔍 开始读取专利PDF: {patent_file}")
        
        # 打开PDF文档
        doc = fitz.open(patent_file)
        total_pages = len(doc)
        print(f"📚 总页数: {total_pages}")
        
        # 存储完整内容
        full_content = []
        
        # 逐页提取文本
        for page_num in range(total_pages):
            if (page_num + 1) % 5 == 0 or page_num == 0:
                print(f"📖 进度: {page_num + 1}/{total_pages} 页")
            
            page = doc[page_num]
            text = page.get_text()
            
            # 添加页面分隔符和内容
            full_content.append(f"{'=' * 80}")
            full_content.append(f"第 {page_num + 1} 页 / 共 {total_pages} 页")
            full_content.append(f"{'=' * 80}")
            full_content.append(text)
            full_content.append("")
        
        doc.close()
        
        print(f"✅ 提取完成！成功读取 {total_pages}/{total_pages} 页")
        
        # 合并所有内容
        complete_text = "\n".join(full_content)
        
        # 统计信息
        total_chars = len(complete_text)
        total_lines = len(complete_text.split('\n'))
        
        print(f"📝 总字符数: {total_chars:,}")
        
        # 保存完整内容
        output_file = "专利CN109980677A_完整内容.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"中国专利CN109980677A完整内容\n")
            f.write(f"{'=' * 60}\n\n")
            f.write(complete_text)
        
        print(f"💾 完整内容已保存到: {output_file}")
        
        # 内容分析
        print(f"\n📊 内容分析:")
        print(f"   总字符数: {total_chars:,}")
        print(f"   总行数: {total_lines:,}")
        print(f"   成功提取页数: {total_pages}/{total_pages}")
        print(f"   提取成功率: {100.0}%")
        
        # 关键词分析
        keywords = ["制氢", "氢能", "电解", "燃料电池", "风力", "太阳能", "光伏", "储能", "控制", "优化", "系统"]
        keyword_counts = {}
        
        for keyword in keywords:
            count = complete_text.count(keyword)
            if count > 0:
                keyword_counts[keyword] = count
        
        if keyword_counts:
            print(f"\n🔑 主要技术关键词:")
            sorted_keywords = sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)
            for keyword, count in sorted_keywords[:10]:  # 显示前10个
                print(f"   {keyword}: {count} 次")
        
        # 创建摘要文件
        summary_file = "专利CN109980677A_内容摘要.txt"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("专利CN109980677A内容摘要\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"📊 统计信息:\n")
            f.write(f"总字符数: {total_chars:,}\n")
            f.write(f"总行数: {total_lines:,}\n")
            f.write(f"总页数: {total_pages}\n\n")
            
            if keyword_counts:
                f.write(f"🔑 主要技术关键词:\n")
                for keyword, count in sorted_keywords:
                    f.write(f"{keyword}: {count} 次\n")
            
            f.write(f"\n✅ 摘要已保存到: {summary_file}\n")
        
        print(f"📋 内容摘要已保存到: {summary_file}")
        print(f"\n🎉 专利文档完整内容读取任务完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取过程中出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = read_patent_pdf()
    if not success:
        sys.exit(1)
