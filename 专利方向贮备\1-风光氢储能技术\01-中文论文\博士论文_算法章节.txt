博士论文 - 算法相关章节摘录
============================================================

==================================================
第26页 (算法相关)
==================================================
2Ⅲ器娄群鑫霁考星差摹罄麓蔗誊耄熟麓藏本文第5章
”
程实验室
电池：容量3kW
1．6论文主要工作
本论文结合国家高技术研究发展计划(863计划)项目“风电制氢与燃料
电池集成系统关键技术研究与示范(SS2014AA052502)”、吉林省科技攻关
项目“离／并网风电．制氢．燃料电池混合系统综合控制技术研究与应用
(20140203003SF)”以及吉林省科技引导项目“离／并网光伏．制氢．燃料电池
集成系统控制技术研究与应用(2015041 1008XH)”，重点分析了风光氢综合
能源系统容量优化配置，建立了风电、光伏、氢储能系统的动态模型，对风光
氢综合能源并网发电系统控制进行了深入研究，提出了基于氢储能的主动型风
电机组、并网光伏阵列控制策略，通过数字仿真和物理实验实现了风光氢综合
能源系统在线能量调控(如图1．4所示)。本论文的主要工作如下：
(1)考虑弃风、弃光及功率波动的风光氢综合能源系统优化配置。以弃
风、弃光耦合制氢和燃料电池车系统场景为基础，重点分析针对不同氢负荷及
未来技术和市场进步，弃风、弃光耦合制氢和燃料电池车的经济性和最优氢储
能容量配置，考虑风、光功率波动基于改进化学反应优化算法的风光氢综合能
源系统经济性分析及容量优化配置，为后续章节提供经济分析基础。
(2)风／光／氢储能系统模型建立与物理验证。本文分别建立了直驱永磁同
步风电机组、并网光伏阵列、质子交换膜燃料电池并网发电系统、碱式电解水
制氢系统模型，通过主电路参数计算和PI参数校验，改善模型不同容量适应性，
提高控制的精确性，利用物理实验系统验证了模型的有效性和正确性，为后续
章节研究提供模型基础。
(3)计及氢储能的主动型风电机组及并网光伏阵列控制策略。针对现有
直驱永磁同步风电机组和并网光伏阵列功率输出受风速、辐照及电网需求影响，
导致风光功率波动及弃风、弃光现象凸显，本文在直驱永磁同步风电机组背靠
背全功率变流器及并网光伏阵列并网逆变器DC侧引入氢储能和化学储能，通
过所提控制策略实现系统整体对外可调、可控，可有效抑制风、光功率波动和
弃风、弃光问题，即主动型风电机组和并网光伏阵列。
(4)典型风光氢综合能源系统在线能量调控策略与实验平台搭建。本文
设计了一种典型风光氢综合能源系统结构，提出了风光氢综合能源系统在线能
量调控策略，开发了风光氢综合能源在线监控系统，并通过PSCAD／EMTDC数
字仿真平台和物理实验平台验证了所提能量调控策略的有效性和正确性。
版权所有(c)国家科技图书文献中心         D24040900252ZX03


==================================================
第28页 (算法相关)
==================================================
第2章考虑弃风／弃光，功率波动的风光氢综合能源系统优化配置
第2章考虑弃风／弃光／功率波动的风光氢综合能源系
统优化配置
2．1引言
目前弃风、弃光消纳及功率平滑方案主要以电加热、风光火打捆以及化学储
能为主，但化学储能寿命期限短，存在环境污染等问题【95，961。随着制储氢技术
的逐渐成熟及成本的不断降低，利用绿色氢储能解决现阶段弃风、弃光及功率波
动问题逐渐被专家学者所关注，然而风光氢储能综合系统容量优化配置是前期比
较重要的基础工作之一【97，981。因此，关于利用氢储能解决弃风、弃光消纳及功
率波动问题的研究，已成为风电、光伏运营商及科研工作者关注热点问题之一。
本章首先提出了利用弃风、弃光电量进行电解水制氢，为氢燃料电池车(Fuel
Cell Electric Vehicle，FCEV)提供能源的设想，并利用可再生能源电力混合优化
模型(Hybrid Optimization Model for Electric Renewables，HOMER)分析软件，
针对我国吉林地区某风场和光伏站实际弃风、弃光情况，进行了弃风、弃光耦合
制氢和燃料电池车系统技术经济分析与容量优化配置，确定了系统中各组成部分
的最优容量，同时考虑技术及市场的进步，对含有不同数量FCEV系统进行经济
性评价，实现弃风、弃光电量优化利用；然后，考虑平抑风电、光伏及风光互补
功率波动前提下，以利润最大为目标，同时考虑环境效益和投资时间价值，在制
氢和燃料电池容量约束下，应用改进化学反应优化算法(Improved Chemical
Reaction Optimization Algorithm，ICROA)优化系统最高利润，及最高利润对应
的制氢装置与燃料电池优化容量。
2．2风光氢综合能源系统简介与经济模型构建
2．2．1风光氢综合能源系统简介
本章主要针对弃风、弃光和功率波动问题，合理优化配置氢储能，来提高风
光运行经济性，系统具体介绍如下：
2．2．1．1弃风、弃光耦合制氢和FCEV综合系统
利用弃风、弃光电量电解水制氢，作为FCEV燃料供给，提高风光利用率，
增加风场和光伏站经济效益，同时可推动FCEV快速发展。弃风、弃光电量制氢
版权所有(c)国家科技图书文献中心         D24040900252ZX03


==================================================
第44页 (算法相关)
==================================================
第2苹考虑弃风／弃光，功率波动的风光氢综合能源系统优化配置
甚至亏损。当日平均氢负荷在480kg．960kg时，系统会取得较高的利润。而随着
电解槽及贮氢罐未来成本的降低，系统达到最高利润的氢负荷逐渐升高。预期到
2050年时，氢负荷约为960kg／d时系统年利润最大，(其中风电弃风消纳年净利
润604．96万元，此时电解槽最优容量为9MW，储氢罐最优容量7000kg，光伏弃
光消纳年净利润206．35万元，此时电解槽最优容量为3．1MW，储氢罐最优容量
2400kg，弃风、弃光消纳年净利润合计811．3l万元，此时电解槽最优容量为
12．1MW，储氢罐最优容量9400kg)，此时弃风和弃光利用率分别达到了34％和
37．4％。
在未来成本变动中，本文仅考虑了主要影响因素，即电解槽及贮氢罐的设备
投资成本。实际上，未来年份中氢气及风电等的价格也会发生变化，且副产品氧
气的市场利润也未被计入系统收益中。故上述所计算的系统收益为保守估计，系
统的实际收益会进一步增高。
2．4基于改进化学反应优化算法的风光氢综合能源系统容量优化
配置
2．4．1化学反应优化算法及其改进
2．4．1一化学反应优化算法简介
化学反应中，将分子所具有的能量定义为势能(potential energy，PE)和动
能(kinetic energy，KE)。
分子势能定义为分子由于其结构特点所具有的能量，在求解优化问题时将其
作为目标函数。若以∞与．厂分别表示分子的结构(或某一可行解)和目标函数，
则有：
嘎=八co)
(2-20)
式中，尸玩为分子09的势能。
分子动能是衡量系统中是否会发生分子反应、所发生分子反应类型的一个标
准。例如，一个分子试图从09状态变为∞’状态时，如果PEo。>PE小或者满足
尸厶+K如>尸既，时，反应就可以发生。需要注意的是，化学反应的发生并不局限
于单个的分子，在化学反应中可能同时包括了若干的分子反应。
CROA中分子的属性如表2．1l所示。
表2．11 CROA中分子的属性
Table
1 Molecular properties of the CROA
分子
化学属性
算法含义
版权所有(c)国家科技图书文献中心         D24040900252ZX03


==================================================
第46页 (算法相关)
==================================================
第2章考虑弃风／弃光／功率波动的风光氢综合能源系统优化配置
式中，ml、m2、m3及m4均为[O，l】区间中的随机数，且四个随机数均为独立生成。
反应后buffer更新为：
buffer=buffer+tempi—KE确,一KE嘶．
(2-29)
(3)分子间无效碰撞
发生分子间无效碰撞的条件为：
％+％+KE+KE≥赐+嚷
(2-30)
设置临时变量temp2，则新分子的分子动能可表示为：
temp2=(心+％+强+KE,02)一嘎+％
(2_31)
计算031’和032’分子的动能为：
j暇姐啦×P(2-32)
<
l弛，：=temp2×(1一p)
l
出'
‘
、
‘7
式中，P为取值范围在[0，1】区间内的随机变量。
(4)合成反应
合成反应发生的条件为：
％+％+瓯+嘎≥晖·
(2-33)
由于在反应过程中能量守恒，因此有：
KE矗=PE一+PE啦七KE执+KE～一PE矗
(2-34w,1
w2
)
m
q
哟
∞
2．4．1．2算法改进
在CROA中，每一次反应发生之后都涉及到分子更新这一步骤。在诸多有
关CROA的文献中，并未对分子更新的具体方法或要求予以明确的规定与说明。
诸多文献中的解释为，任意一种合理的能使所产生的新分子位于远离或邻近原有
分子位置处的方法均为可行的方案。
在CROA中，分子与墙无效碰撞、分子分解和分子间无效碰撞的分子结构
更新表达式为【106】：
缈。(f)=国(f)+8(0
(2-35)
式中，6(f)为第i维高斯分布扰动。
考虑到粒子群算法中的粒子存在着与CROA中的分子相似的要素，且其具
有收敛速度快的突出优势，因此本文将二者加以结合，得到性能更为优越的改进
算法[107]。
粒子群算法中，通过对最优值的跟踪完成对最优位置的搜索，核心计算公式
即为粒子的速度与位置向量更新公式：
屹=尼％+rifandO(圪胁一局)+q2randO(P鲥b删一蜀)
(2-36)
邑+l=如+屹
(2-37)
版权所有(c)国家科技图书文献中心         D24040900252ZX03


==================================================
第47页 (算法相关)
==================================================
华北电力大学博士学位论文
式中，石d为粒子i的当前所在位置分量，所d为粒子f在约束范围内的d维上的
速度分量，P协吲为该粒子本身在迭代进化过程中所找到的历史最优解的位置，
尸肋删为整个粒子种群在迭代进化过程中所找到的历史最优解的位置，石抖1为粒
子i的更新位置，k为惯性因子，用于控制算法的探索范围与计算精度，取值区
间为[0，l】，r／l、r12为加速度系数，又称学习因子，用以联系权衡粒子本身所具有
的记忆信息与整个种群间的共享信息，rand()为[0，1]区间的服从均匀分布的伪
随机函数。
ICROA的主要思路是对分子更新中的分子扰动6(f)进行改进，利用粒子
群优化算法中粒子的速度与位置更新公式对6(f)进行规定如下：
a(i)=七％+rllrandO(吃蜘f—co(i))
(2·38)
式中，％为分子结构中第i维的运动速度；乓曲吲t为全局分子在反应过程中第i
维的历史最优解的位置。
2．4．2风电、光伏功率平滑分析
风电、光伏功率指数平滑计算为【108]：
竞且，(，)=q+勿·l+q·z2
式中，o，，(，)为t时段对t+l时段的风电、光伏功率预测值，
其中a，、b，、o参数的计算公式为：
q=3曩■-3啦，，+础¨
(2．39)
，为预测超前期，
6，2赤[(6嘲)如一2(5地)啦’，+(4砌)蛾]
q 2赤[啦，，_2p‰t'J+啦，，]
q 2而虿【．％'f⋯f％'，J
式中，
fp(1)
f。w_pv，‘
{患，，
【患，，
。c瓦川+
口也’r+
口啦，，+
1一口
l一口
l一口
(2．40)
(2．41)
(2．42)
也'(，_I)
患’(f-1)
(2．43)
啦’(f-1)
式中，Pw-pv’r为t时段风电、光伏输出功率，a为平滑常数，也，为t时段一次功
率平滑值，也巾-1)为f一1时段一次功率平滑值，息，为f时段二次功率平滑值，
啦肛。)为t-1时段二次功率平滑值，垲，，，为t时段三次功率平滑值，啦m-I)为t-1
时段三次功率平滑值。
本文采用平滑效果较好的三次功率平滑值作为风电、光伏上网功率，其它脉
版权所有(c)国家科技图书文献中心         D24040900252ZX03


==================================================
第50页 (算法相关)
==================================================
第2章考虑弃风／弃光，功率波动的风光氢综合能源系统优化配置
式中，P。1m双为电解槽的容量限值，其值为风电功率正波动最大值。
(3)燃料电池容量约束
0≤圪。≤层函。
(2—50)
式中，尸fcmax为燃料电池的容量限值，其值为风电功率负波动最大值。
2．4．4基于iCROA的系统经济性分析与容量优化配置
分子维度包括：电解槽容量，燃料电池容量，分子动能，分子势能。由于此
算法求取分子势能最低值为目标，但本文目标为系统利润最大，故系统目标函数
(分子势能)可表达为参考常数与系统净利润之差。首先初始化算法参数，然后
判断符合四种反应类型中的哪一种，反应之后，进行粒子更新，判断是否达到最
小值，如果达到了最小值，输出目标函数值(系统最大利润值)和对应的解(电
解槽和燃料电池的优化容量)，如果没达到最小值，返回初始化再次进行反应类
型判断，直到满足反应停止条件为止。
基于ICROA的系统经济性分析与容量优化配置流程如图2．15所示。由图可
知，首先对风电、光伏功率进行平滑处理，得到三次指数平滑后风电、光伏平滑
功率值曩3：，，，进一步计算出电解槽吸纳的风电功率P。1，，和燃料电池发出的功率
Pfc扣然后，算法初始化，确定分子维度、分子动能、分子势能及相关参数等。
利用ICROA优化出系统最大利润及其对应的电解槽和燃料电池在容量约束内的
最优容量。最后输出利润值及对应的不同时刻的风电功率平抑效果，即电解槽吸
纳和燃料电池补充风电、光伏功率情况。
2．4．5算例分析
2．4．5．1参数设置
利用ICROA对吉林地区某风场和光伏站进行分析，风场装机容量为48MW
(24台2MW直驱风机)，光伏站装机20MW(40组0．5MW光伏阵列)。根据实
测风速、太阳辐照及环境温度，设定a--0．15，风场、光伏站及风光耦合功率计算
结果如图2．16至图2．18所示，算法参数设置见表2．12至表2．15。
表2．12 ICROA参数设置[109]
Table 2．1 2 Parameter settings of ICROA
名称
变量
数值
分子维度
解集数
动能损失率
NVars
Popsize
KElossrate
4
2000
0．3
版权所有(c)国家科技图书文献中心         D24040900252ZX03


==================================================
第58页 (算法相关)
==================================================
第2章考虑弃风／弃光／功率波动的风光氢综合能源系统优化配置
燃料电池容量(0．05MW)。根据最优的电解槽容量和燃料电池容量，计算出各成
本效益参数。
在风光氢综合场景下，对ICROA优化结果的快速性和寻优能力进行讨论，
与原始CROA和粒子群算法对比分析，三种算法计算结果见表2．17。
表2．17三种算法经济分析与容量优化结果对比
Table 2．1 7 Economic evaluation results of three algorithms
■■■■■■■■■■■■■■■■■■●■●■■■■■■■■●■■●●■●■■●■●●■■■■■■■■■■■■■■■■■_■■_■■●■■■■■■●■■■■■■●■■●■_■■●■■■■■■■■■■■■■■●■■●■■■■■■■■●■■■■■■■■一]IV■■■■■■■■■■■■■■■■■■■■■■■■■■■■●■■■■●■■■■●■●■●__■■一
利润最大值
电解槽最优容量
燃料电池最优容量
(万元／年)
(MW)
(MW)
迭代数
30
50
100
200
30
50
100
200
30
50
100
200
CROA
1065
1206
183 1
1837
9．08
11．03
13．03
13．06
0．01
0．02
0．04
0．05
ICROA
1626
1834
1835
1837
12．04
13．03
13．04
13．06
O．03
0．04
0．05
0．05
粒子群
1834
1836
1726
1837
13．03
13．05
12．08
13．06
O．04
O．05
O．02
0．05
分析可知，CROA收敛所需的迭代次数最多，大约在迭代100次之后达到收
敛，粒子群算法虽然收敛速度快，然而在多次优化过程中曾有陷入局部最优解的
情况出现。相比之下，ICROA在迭代50次左右时就基本达到收敛，同时不会陷
入局部最优中。因此，ICROA在迭代速度以及避免陷入局部最优等方面的表现
要优于两种原始算法，能够同时融合两种算法的优点，具备较好的寻优能力。
2．5本章小结
本章首先利用HOMER软件，对吉林地区某风电场的具体弃风、弃光与制氢
和燃料电池车系统结合，并对其进行了技术经济性分析与容量优化配置，然后基
于ICROA对风光氢综合能源系统功率平滑经济性进行了分析，同时优化出电解
槽和燃料电池平滑风、光波动功率的最优容量，主要小结如下：
(1)弃风、弃光耦合制氢和燃料电池车系统的初始投资成本和氢负荷(燃
料电池车数量)对系统经济效益及容量配置影响较大。
(2)随着科技及市场的进步(主要影响因素：制氢装置成本和储氢成本降
低)，燃料电池车达到一定数量时，系统能够更好地对弃风、弃光电量进行消纳，
增加风场、光伏站的经济效益。
(3)通过本章研究可得，通过利润最大化规划合理数量的燃料电池车吸纳
弃风和弃光电量，既增加风场和光伏站的经济效益，同时提高了风光资源利用率，
降低了污染物排放水平。因此，弃风／弃光耦合制氢和燃料电池车系统对我国大
力推进燃料电池车商业化发展具有一定的经济及环境指导意义。
(4)利用制氢和燃料电池一定程度上平滑风电、光伏功率波动，同时风场
和光伏站可获得一定的利润收入，考虑风光天然互补特性尤其明显，经济性上优
于传统化学储能。
(5)应用CROA及其改进算法，可快速、准确的优化出风场、光伏站利润
43
版权所有(c)国家科技图书文献中心         D24040900252ZX03


==================================================
第76页 (算法相关)
==================================================
第3章风／光／氢储能系统模型构建与物理验证
3．4．2最大功率跟踪算法
目前光伏发电最大功率跟踪主流算法主要包括：恒定电压控制法(CVT)、
爬山法(也称扰动观察法Perturb and Observe Method，简称P&O)、导纳增量法
(Incremental Conductance Algorithm，简称IncCond)等。
为了适应太阳辐射强度快速变化，作者在文献[117】中提出的基于功率变化
率改进扰动观察法进行最大功率点跟踪(maximum power．point tracking，MPPT)，
与文献[118】提到的传统改进扰动观察法相比，跟踪速度相对较快，稳定性较好。
基于功率变化率改进扰动观察法具体算法流程如图3．23所示。
图3．23基于功率变化率改进扰动观察法算法流程图
Fig．3-23 Flowchart of the P&O algorithm based on power change rate
由图可知，通过本时刻测量的电压U(七)与电流I(七)同上时刻记录的电
压U(k-1)和电流，(肛1)经计算得到功率变化率dP。为避免频繁扰动，设定
功率变化率下限s1，如果dP勺l，则认为功率输出不变，不进行电压扰动；否则，
再判断如果dP>c2，对dP进行修正，然后按功率变化率变步长进行电压扰动。
3．4．3并网控制策略
光伏发电系统控制主要由三部分组成：光伏阵列控制部分，最大功率点追踪
6l
版权所有(c)国家科技图书文献中心         D24040900252ZX03

