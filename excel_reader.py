#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件阅读器
"""

import pandas as pd
import sys
from pathlib import Path

def read_excel_file(file_path):
    """读取Excel文件"""
    try:
        # 读取所有工作表
        excel_file = pd.ExcelFile(file_path)
        print(f"工作表列表: {excel_file.sheet_names}")
        
        for sheet_name in excel_file.sheet_names:
            print(f"\n{'='*60}")
            print(f"📊 工作表: {sheet_name}")
            print(f"{'='*60}")
            
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            print(f"数据形状: {df.shape}")
            print("\n数据内容:")
            print(df.to_string(index=False))
            print()
            
    except Exception as e:
        print(f"读取错误: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        if Path(file_path).exists():
            read_excel_file(file_path)
        else:
            print("文件不存在")
    else:
        print("请提供Excel文件路径")