#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正专利申请书剩余段落编号的脚本
"""

def fix_remaining_numbers(file_path):
    """修正专利申请书剩余的段落编号"""
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 定义需要修正的编号映射（从[0046]开始）
    number_mapping = {
        '[0046]': '[0039]',
        '[0047]': '[0040]',
        '[0048]': '[0041]',
        '[0049]': '[0042]',
        '[0050]': '[0043]',
        '[0051]': '[0044]',
        '[0052]': '[0045]',
        '[0053]': '[0046]',  # 第一个[0053]
        '[0054]': '[0047]',  # 第一个[0054]
        '[0055]': '[0048]',
        '[0056]': '[0049]',
        '[0057]': '[0050]',
        '[0058]': '[0051]',
        '[0059]': '[0052]',
        '[0060]': '[0053]',
        '[0061]': '[0054]',
        '[0062]': '[0055]',
        '[0063]': '[0056]',
        '[0064]': '[0057]',
        '[0065]': '[0058]',
        '[0066]': '[0059]',
        '[0067]': '[0060]',
        '[0068]': '[0061]',
        '[0069]': '[0062]',
        '[0070]': '[0063]',
        '[0071]': '[0064]',
        '[0072]': '[0065]',
        '[0073]': '[0066]',
        '[0074]': '[0067]',
        '[0075]': '[0068]',
        '[0076]': '[0069]',
        '[0077]': '[0070]',
        '[0078]': '[0071]',
        '[0079]': '[0072]',
        '[0080]': '[0073]',
        '[0081]': '[0074]',
        '[0082]': '[0075]',
        '[0083]': '[0076]',
        '[0084]': '[0077]',
        '[0085]': '[0078]',
        '[0086]': '[0079]',
        '[0087]': '[0080]',
        '[0088]': '[0081]',
        '[0089]': '[0082]',
        '[0090]': '[0083]'
    }
    
    # 按照映射替换编号
    for old_num, new_num in number_mapping.items():
        content = content.replace(old_num, new_num)
    
    # 处理重复的段落编号问题
    # 在具体实施方式部分有多个重复的[0053]、[0054]等
    # 需要手动处理这些重复编号
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已修正剩余编号: {file_path}")

if __name__ == "__main__":
    file_path = "专利方向贮备/4-绿电制氨、掺氨燃烧系统数字孪生/相关专利申请书/一种基于数字孪生技术的绿电制氨掺氨燃烧全链路智能协同控制系统.txt"
    fix_remaining_numbers(file_path)