#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整修正专利申请书段落编号的脚本
"""

def complete_fix(file_path):
    """完整修正专利申请书的段落编号和重复内容"""
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 首先处理重复内容问题
    # 删除文档末尾的重复段落
    lines = content.split('\n')
    
    # 找到第一个重复的[0046]位置（在具体实施方式部分）
    first_duplicate_index = -1
    for i, line in enumerate(lines):
        if '[0046] 在预期应用场景中' in line:
            first_duplicate_index = i
            break
    
    if first_duplicate_index != -1:
        # 删除从第一个重复段落开始的所有内容
        lines = lines[:first_duplicate_index]
    
    # 重新组合内容
    content = '\n'.join(lines)
    
    # 现在修正所有编号问题
    # 发明内容部分已经修正过了，现在主要修正具体实施方式部分的重复编号
    
    # 处理具体实施方式部分的编号错误
    # 在具体实施方式中，有些编号是重复的，需要重新编号
    
    # 找到具体实施方式部分的开始
    lines = content.split('\n')
    result_lines = []
    in_implementation = False
    current_number = 33  # 具体实施方式从[0033]开始
    
    for line in lines:
        if line.strip() == '具体实施方式':
            in_implementation = True
            result_lines.append(line)
            continue
        
        if in_implementation and line.strip().startswith('['):
            # 在具体实施方式部分，重新编号
            if line.strip().startswith('[0'):
                # 提取段落内容（去掉编号）
                content_part = line.split('] ', 1)
                if len(content_part) > 1:
                    new_line = f'[{current_number:04d}] {content_part[1]}'
                    result_lines.append(new_line)
                    current_number += 1
                else:
                    result_lines.append(line)
            else:
                result_lines.append(line)
        else:
            result_lines.append(line)
    
    # 写回文件
    final_content = '\n'.join(result_lines)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(final_content)
    
    print(f"已完整修正文件: {file_path}")

if __name__ == "__main__":
    file_path = "专利方向贮备/4-绿电制氨、掺氨燃烧系统数字孪生/相关专利申请书/一种基于数字孪生技术的绿电制氨掺氨燃烧全链路智能协同控制系统.txt"
    complete_fix(file_path)