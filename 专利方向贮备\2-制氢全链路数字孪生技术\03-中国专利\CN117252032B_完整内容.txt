(19)国家知识产权局
P
(21)申请号 202311490394.9
(22)申请日 2023.11.10
(65) 同一申请的已公布的文献号
申请公布号 CN 117252032 A
(43)申请公布日 2023.12.19
(73)专利权人 三峡科技有限责任公司
(12)发明专利
地址 101599 北京市密云区水源路南侧A-
04地块2#商业办公楼2层1单元-220
(72)发明人 陈明轩赵雄郁章涛张宝平
李冬芳 王文雍贾宏晶
(74) 专利代理机构 北京立成智业专利代理事务
所(普通合伙)11310
专利代理师 张厚山
(51) Int.Cl.
G06F 30/20 (2020.01)
(54)发明名称
碱性电解水制氢系统的数字孪生体构建方
法、装置及设备
(57) 摘要
为解决现有技术的不足,本发明提供了一种
碱性电解水制氢系统的数字孪生体构建方法,包
括以下步骤:步骤1获取实际碱性电解水制氢系
统的系统参数和数字孪生体的目标性能;步骤2
根据所述系统参数,建立多个仿真模型;步骤3基
于所述仿真模型,得到初始数字孪生体;步骤4将
所述初始数字孪生体与所述实际碱性电解水制
氢系统进行数据对接,对所述初始数字孪生体进
行测试和调整,直到所述初始数字孪生体满足所
述目标性能;将所得满足所述目标性能的初始数
字孪生体作为该实际碱性电解水制氢系统的数
字孪生体。本发明解决了现有技术模型不全,仿
真效果差的问题。
(10)授权公告号 CN 117252032 B
(45)授权公告日 2024.02.13
G06F 30/27 (2020.01)
G06F 111/10 (2020.01)
G06F 119/02 (2020.01)
(56)对比文件
CN 116227248 A,2023.06.06
CN 113793649 A, 2021.12.14
CN 113122867 A,2021.07.16
WO 2021108680 A1,2021.06.03
审查员 王婷婷
权利要求书3页 说明书11页附图1页
获取实际碱性电解水制氢系统的系统参数和数字孪生体的目标性能
根据系统参数,建立多个仿真模型
基于仿真模型,得到初始数字孪生体
将初始数字孪生体与所述实际碱性电解水制氢系统进行数据对接,对
初始数字孪生体进行测试和调整,直到初始数字孪生体满足所述目标
性能
将初始数字孪生体作为数字孪生体
CN 117252032 B
CN 117252032 B
权利要求书
1/3页
1.一种碱性电解水制氢系统的数字孪生体构建方法,其特征在于,包括以下步骤:
步骤1 获取实际碱性电解水制氢系统的系统参数和数字孪生体的目标性能,且所述系
统参数能够得到所述目标性能;
步骤2 根据所述系统参数,建立多个仿真模型;
步骤3 基于所述仿真模型,得到初始数字孪生体;
步骤4 将所述初始数字孪生体与所述实际碱性电解水制氢系统进行数据对接,对所述
初始数字孪生体进行测试和调整,直到所述初始数字孪生体满足所述目标性能;
将步骤4所得满足所述目标性能的初始数字孪生体作为该实际碱性电解水制氢系统的
数字孪生体;
步骤2所述根据所述系统参数,建立多个仿真模型的方法包括:
步骤2.1 从所述系统参数中获取所述实际碱性电解水制氢系统中的工况参数和边界
参数;
步骤2.2 基于所述工况参数,通过预设方程描述碱性电解槽运行过程,得到模拟碱性
电解水制氢系统的机理模型;
根据所述工况参数确定所述模拟碱性电解水制氢系统的控制策略;
根据所述边界参数,建立所述模拟碱性电解水制氢系统的动态模型;
步骤2.3 根据所述控制策略、所述碱性电解槽运行过程以及预设控制方法,建立所述
模拟碱性电解水制氢系统的运行环境模型;
所述机理模型为:使用碱性电解槽中可逆电压的数值模型来描述可逆电压与温度和压
力之间的关系,建立碱性电解槽欧姆过电位的数值模型描述隔膜阻抗、电解液阻抗、气泡、
阻抗、电极材料阻抗的关系;
所述动态模型包括:
电解槽温升特性建模,通过电解槽溫升特性模型能推导出不同的工作环境中电解槽维
持电解反应的最小电解电流,计算得到电解槽在不同工作环境下的保温功率;
电解槽功率调节特性建模,明确电解槽的功率调节特性使电解槽的输出功率稳定在安
全区间内;
电解槽产氢特性建模,以电解槽正常运行阶段等效电阻抗随温度的变化公式为基础,
结合正常运行阶段的伏安特性,再根据电解反应中的电荷守恒及法拉第电解定律推导出产
氢量;
电解槽分离罐压强建模,对侧放的圆柱形容器液面高度与液体体积的函数关系进行近
似处理,得到电解槽工作过程中气液分离罐內的液面高度,通过理想气体状态方程得到气
液分离罐内的压强;
所述运行环境模型为:
将电解槽等效电阻抗基于电路理论的数学表达式转换为仅与温度相关的数学特征表
达式,基于阻抗泛化模型,以槽温为唯一变量推导电解槽经济额定功率,作为电解槽运行功
率上限;
以电化学反应热平衡方程为基础,得到电-热模型数学方程,进而推导电解槽保温功率
仅与温度相关的模型,作为电解槽运行功率下限;
利用电-热模型数学方程对时间积分,得到温升特性模型,将所有以温度为唯一变量的
2
CN 117252032 B
权利要求书
2/3页
数字孪生模型与温升模型联立,对时间分段简化后得到升温过程中的功率调节模型,以等
效电阻抗随温度的变化为基础得到电解电流,并进一步推得产氢特性模型。
2.根据权利要求1所述碱性电解水制氢系统的数字孪生体构建方法,其特征在于,步骤
3所述基于所述仿真模型,得到初始数字孪生体的方法包括:
步骤3.1 整合所述机理模型、所述运行环境模型以及所述动态模型,得到所述模拟碱
性电解水制氢系统;
步骤3.2 存储所述模拟碱性电解水制氢系统的系统状态参数;
步骤3.3 基于所述运行环境模型,生成用于调整所述系统状态参数和所述控制策略的
自适应控制算法;
步骤3.4 整合所述模拟碱性电解水制氢系统、存储的系统状态参数以及自适应控制算
法,得到所述初始数字孪生体。
3.根据权利要求2所述碱性电解水制氢系统的数字孪生体构建方法,其特征在于,步骤
3.1还包括:通过预设优化算法,对所述机理模型、运行环境模型以及动态模型进行优化。
4.根据权利要求1所述碱性电解水制氢系统的数字孪生体构建方法,其特征在于,步骤
4所述对所述初始数字孪生体进行测试和调整,直到所述初始数字孪生体满足所述目标性
能的方法包括:
步骤4.1 从所述实际碱性电解水制氢系统获取实际测试数据,并将所述实际测试数据
导入所述初始数字孪生体;
步骤4.2 根据预设监测算法,对所述初始数字孪生体中的模拟碱性电解水制氢系统根
据所述实际测试数据进行运行的过程进行实时监测,得到实时数据;
步骤4.3 对所述实时数据进行分析,判断所述模拟碱性电解水制氢系统是否存在问
题;
步骤4.4 在所述模拟碱性电解水制氢系统不存在问题的情况下,利用预设仿真模型和
所述实际碱性电解水制氢系统的实际运行数据验证所述模拟碱性电解水制氢系统的系统
性能是否符合预设条件;
步骤4.5 在所述系统性能符合所述预设条件的情况下,对所述初始数字孪生体进行多
次测试和验证,并根据测试结果和验证结果调整所述初始数字孪生,直到所述初始数字
孪生体满足所述目标性能。
5.根据权利要求4所述碱性电解水制氢系统的数字孪生体构建方法,其特征在于,步骤
4.5所述根据测试结果和验证结果调整所述初始数字孪生的方法包括:
况;
步骤4.5.1 根据所述测试结果和所述验证结果,确定所述初始数字孪生体的运行情
步骤4.5.2 根据所述运行情况调整控制策略和优化系统状态参数。
6.根据权利要求1所述碱性电解水制氢系统的数字孪生体构建方法,其特征在于,所述
碱性电解水制氢系统的数字孪生体构建方法还包括:
步骤5 按照预设周期对所述数字孪生体进行维护和升级。
7.根据权利要求1所述碱性电解水制氢系统的数字孪生体构建方法,其特征在于,所述
碱性电解水制氢系统的数字孪生体构建方法还包括:在模拟碱性电解水制氢系统不存在问
题的情况下,确定预设控制算法,所述初始数字孪生体通过预设控制算法控制实际碱性电
3
CN 117252032 B
解水制氢系统的运行。
8.一种装置,包括:
权利要求书
3/3页
获取模块,所述获取模块用于执行权利要求1-7任一所述碱性电解水制氢系统的数字
孪生体构建方法中的步骤1所含方法步骤;
建立模块,所述建立模块用于执行权利要求1-7任一所述碱性电解水制氢系统的数字
孪生体构建方法中的步骤2所含方法步骤;
得到模块,所述得到模块用于执行权利要求1-7任一所述碱性电解水制氢系统的数字
孪生体构建方法中的步骤3所含方法步骤;
调整模块,所述调整模块用于执行权利要求1-7任一所述碱性电解水制氢系统的数字
孪生体构建方法中的步骤4所含方法步骤;
作为模块,所述作为模块用于确认所述碱性电解水制氢系统的数字孪生体。
9.一种设备,包括:处理器、通信接口、存储器和通信总线,其中,处理器、通信接口和存
储器通过通信总线完成相互间的通信;其中,存储器,用于存储计算机程序;其特征在于,处
理器,用于通过运行所述存储器上所存储的所述计算机程序来执行权利要求1-7任一所述
碱性电解水制氢系统的数字孪生体构建方法中的方法步骤。
10.一种计算机可读的存储介质,该存储介质中存储有计算机程序,其特征在于,该计
算机程序被设置为运行时执行权利要求1-7任一所述碱性电解水制氢系统的数字孪生体构
建方法中的方法步骤。
4
CN 117252032 B
技术领域
说明书
碱性电解水制氢系统的数字孪生体构建方法、装置及设备
1/11页
[0001] 本发明涉及数字孪生制备技术领域,尤其涉及一种碱性电解水制氢系统的数字孪
生体构建方法、装置及设备。
背景技术
[0002] 碱性电解水制氢是将水电解为氢气和氧气,不会产生污染气体,因而在现代制氢
领域具有重要的应用。碱性电解水制氢涉及到电气、热、化工流程等场景,由于涉及到的系
统众多,数字孪生技术对于碱性电解水制氢就显得格外重要。然而,现有碱性电解水制氢系
统的数字孪生体的构建方法,大部分只对碱性电解水制氢装置或配套的电力管控系统进行
监测和分析,或只针对电解水效率进行仿真分析,缺少整体的碱性电解水制氢系统的数字
孪生体构建方案和应用案例。
[0003] 可见,现有技术没有对碱性电解水制氢系统的实际应用场景的多方面要素进行融
合,其仿真分析结果不全面,现有数字孪生体无法为碱性电解水制氢系统的安全性、稳定
性、寿命、故障进行有效的预测,也无法提供有效的解决方案。
[0004] 同时,现有碱性电解水制氢系统的数字孪生体的构建方法没有考虑环境因素,无
法将电力、化学、环境等多因素相互关联,没有用数字化手段建立碱性电解水制氢系统相对
完整的仿真体系和模型,没有将碱性电解水制氢系统运行过程中的多方面数据进行充分的
融合分析,仿真模型的参数不完整,不能还原实际运行过程中的系统状态,没有连续性,智
能化、数据融合度不高,无法在全生命周期过程中对碱性电解水制氢系统进行动态分析和
评估。
发明内容
[0005] 为解决现有技术的不足,本发明提供了一种碱性电解水制氢系统的数字孪生体构
建方法,包括以下步骤:
[0006] 步骤1 获取实际碱性电解水制氢系统的系统参数和数字孪生体的目标性能,且所
述系统参数能够得到所述目标性能;
[0007]
步骤2 根据所述系统参数,建立多个仿真模型;
[0008] 步骤3 基于所述仿真模型,得到初始数字孪生体;
[0009] 步骤4 将所述初始数字孪生体与所述实际碱性电解水制氢系统进行数据对接,对
所述初始数字孪生体进行测试和调整,直到所述初始数字孪生体满足所述目标性能;
[0010] 将步骤4所得满足所述目标性能的初始数字孪生体作为该实际碱性电解水制氢系
统的数字孪生体。
[0011] 进一步的,步骤2所述根据所述系统参数,建立多个仿真模型的方法包括:
[0012]
步骤2.1 从所述系统参数中获取所述实际碱性电解水制氢系统中的工况参数和
边界参数;
[0013]
步骤2.2 基于所述工况参数,通过预设方程描述碱性电解槽运行过程,得到模拟
5
CN 117252032 B
碱性电解水制氢系统的机理模型;
说明书
[0014] 根据所述工况参数确定所述模拟碱性电解水制氢系统的控制策略;
[0015] 根据所述边界参数,建立所述模拟碱性电解水制氢系统的动态模型;
2/11页
[0016] 步骤2.3 根据所述控制策略、所述碱性电解槽运行过程以及预设控制方法,建立
所述模拟碱性电解水制氢系统的运行环境模型。
[0017] 进一步的,步骤3所述基于所述仿真模型,得到初始数字孪生体的方法包括:
[0018] 步骤3.1 整合所述机理模型、所述运行环境模型以及所述动态模型,得到所述模
拟碱性电解水制氢系统;
[0019] 步骤3.2 存储所述模拟碱性电解水制氢系统的系统状态参数;
[0020] 步骤3.3 基于所述运行环境模型,生成用于调整所述系统状态参数和所述控制策
略的自适应控制算法;
[0021] 步骤3.4 整合所述模拟碱性电解水制氢系统、存储的系统状态参数以及自适应控
制算法,得到所述初始数字孪生体。
[0022] 进一步的,步骤3.1还包括:通过预设优化算法,对所述机理模型、运行环境模型以
及动态模型进行优化。
[0023] 进一步的,步骤4所述对所述初始数字孪生体进行测试和调整,直到所述初始数字
孪生体满足所述目标性能的方法包括:
[0024] 步骤4.1 从所述实际碱性电解水制氢系统获取实际测试数据,并将所述实际测试
数据导入所述初始数字孪生体;
[0025] 步骤4.2 根据预设监测算法,对所述初始数字孪生体中的模拟碱性电解水制氢系
统根据所述实际测试数据进行运行的过程进行实时监测,得到实时数据;
[0026] 步骤4.3 对所述实时数据进行分析,判断所述模拟碱性电解水制氢系统是否存在
问题;
[0027] 步骤4.4 在所述模拟碱性电解水制氢系统不存在问题的情况下,利用预设仿真模
型和所述实际碱性电解水制氢系统的实际运行数据验证所述模拟碱性电解水制氢系统的
系统性能是否符合预设条件;
[0028]
步骤4.5 在所述系统性能符合所述预设条件的情况下,对所述初始数字孪生体进
行多次测试和验证,并根据测试结果和验证结果调整所述初始数字孪生,直到所述初始数
字孪生体满足所述目标性能。
[0029]
包括:
进一步的,步骤4.5所述根据测试结果和验证结果调整所述初始数字孪生的方法
[0030] 步骤4.5.1 根据所述测试结果和所述验证结果,确定所述初始数字孪生体的运行
情况;
[0031] 步骤4.5.2 根据所述运行情况调整所述控制策略和优化所述系统状态参数。
[0032] 进一步的,所述碱性电解水制氢系统的数字孪生体构建方法还包括:
[0033] 步骤5 按照预设周期对所述数字孪生体进行维护和升级。
[0034]
进一步的,所述碱性电解水制氢系统的数字孪生体构建方法还包括:在模拟碱性
电解水制氢系统不存在问题的情况下,确定预设控制算法,所述初始数字孪生体通过预设
控制算法控制实际碱性电解水制氢系统的运行。
6
CN 117252032 B
说明书
[0035] 本发明目的之二在于提供了一种装置,包括:
3/11页
[0036] 获取模块,所述获取模块用于执行上述碱性电解水制氢系统的数字孪生体构建方
法中的步骤1所含方法步骤;
[0037] 建立模块,所述建立模块用于执行上述碱性电解水制氢系统的数字孪生体构建方
法中的步骤2所含方法步骤;
[0038] 得到模块,所述得到模块用于执行上述碱性电解水制氢系统的数字孪生体构建方
法中的步骤3所含方法步骤;
[0039] 调整模块,所述调整模块用于执行上述碱性电解水制氢系统的数字孪生体构建方
法中的步骤4所含方法步骤;
[0040] 作为模块,所述作为模块用于确认所述碱性电解水制氢系统的数字孪生体。
[0041]
本发明目的之三在于提供了一种设备,包括:处理器、通信接口、存储器和通信总
线,其中:处理器、通信接口和存储器通过通信总线完成相互间的通信;存储器,用于存储计
算机程序;处理器,用于通过运行所述存储器上所存储的所述计算机程序来执行上述碱性
电解水制氢系统的数字孪生体构建方法中的方法步骤。
[0042] 本发明目的之四在于提供一种计算机可读的存储介质,该存储介质中存储有计算
机程序,该计算机程序被设置为运行时执行上述碱性电解水制氢系统的数字孪生体构建方
法中的方法步骤。
[0043] 本发明的有益效果在于:本发明从碱性电解水制氢系统的实际出发,充分考虑数
字孪生体需要集成关联的各种仿真分析模型,建立更贴近实际场景需要、可动态更新仿真
计算的碱性电解水制氢系统的数字孪生体。解决了相关技术中存在没有综合考虑碱性电解
水制氢系统多方面因素,导致包含的模型不全,仿真效果差的问题。
附图说明
[0044] 图1所示为本发明碱性电解水制氢系统的数字孪生体构建方法的步骤示意图。
具体实施方式
[0045] 现在结合附图对本发明作进一步详细的说明。这些附图均为简化的示意图,仅以
示意方式说明本发明的基本结构,因此其仅显示与本发明有关的构成。
[0046] 本发明示例性的提供了一种碱性电解水制氢系统的数字孪生体构建方法,如图1
所示,包括如下步骤:
[0047] 步骤1:获取实际碱性电解水制氢系统的系统参数和数字孪生体的目标性能,且所
述系统参数能够得到所述目标性能。
[0048] 其中:获取实际碱性电解水制氢系统的系统参数:需要确定实际碱性电解水制氢
系统中的工况参数,如工作电压、电流,电解液温度、压力等。
[0049] 获取实际碱性电解水制氢系统中的边界参数,如入口进料、环境温度等,这些系统
参数将会影响到实际碱性电解水制氢系统的性能。
[0050] 上述物理参数可以通过对现有实际碱性电解水制氢系统进行测量和分析获得。
[0051]
另外,因为数字孪生体用于对实际碱性电解水制氢系统进行仿真分析,进而对碱
性电解水制氢系统的安全性、稳定性、寿命、故障进行有效的预测并得出解决方案,因此,确
7
CN 117252032 B
说明书
定了实际碱性电解水制氢系统的性能要求,也就能够确定数字孪生体的目标性能。
[0052] 步骤2:根据所述系统参数,建立多个仿真模型。
4/11页
[0053] 其中所述仿真模型可以是:机理模型、运行环境模型、动态模型等,通过上述仿真
模型,模拟实际碱性电解水制氢系统的工作原理、工作过程、实际性能等,可以实现综合考
虑环境因素对碱性电解水制氢系统的影响。
[0054]
步骤3:基于所述仿真模型,得到初始数字孪生体。
[0055] 通过步骤2的仿真模型,可以仿真实际碱性电解水制氢系统的实际工作场景,并模
拟实际碱性电解水制氢系统的系统性能。因此,整合上述仿真模型,可以得到初始数字孪生
体。初始数字孪生体表示还需要对其进行测试、验证以及调整,使其能够更好模拟仿真实际
碱性电解水制氢系统。
[0056] 步骤4:将所述初始数字孪生体与所述实际碱性电解水制氢系统进行数据对接,对
所述初始数字孪生体进行测试和调整,直到所述初始数字孪生体满足所述目标性能。
[0057] 通过对比实际碱性电解水制氢系统和初始数字孪生体的性能,并使用实际碱性电
解水制氢系统的数据对初始数字孪生体进行测试,根据测试结果对初始数字孪生体的仿真
模型、系统参数以及控制策略进行调整,确保初始数字孪生体的准确性和可靠性即满足目
标性能。
[0058] 将步骤4所得满足所述目标性能的初始数字孪生体作为该实际碱性电解水制氢系
统的数字孪生体。
[0059] 当初始数字孪生体满足目标性能时,表示初始数字孪生体可以准确对实际碱性电
解水制氢系统进行仿真分析,因此可以将初始数字孪生体应用到实际的碱性电解水制氢系
统中,以提高系统性能和可靠性,即将初始数字孪生体作为数字孪生体。将数字孪生体应用
到实际的碱性电解水制氢系统中,以提高系统性能和可靠性。
[0060] 本发明示例性的提供了一种步骤2根据所述系统参数,建立多个仿真模型的方法,
包括:
[0061]
步骤2.1 从所述系统参数中获取所述实际碱性电解水制氢系统中的工况参数和
边界参数;
[0062] 步骤2.2 基于所述工况参数,通过预设方程描述碱性电解槽运行过程,得到模拟
碱性电解水制氢系统的机理模型;
[0063] 根据所述工况参数确定所述模拟碱性电解水制氢系统的控制策略;
[0064] 根据所述边界参数,建立所述模拟碱性电解水制氢系统的动态模型;
[0065] 步骤2.3 根据所述控制策略、所述碱性电解槽运行过程以及预设控制方法,建立
所述模拟碱性电解水制氢系统的运行环境模型。
[0066] 例如:需要建立一个碱性电解水制氢系统的机理模型,来模拟碱性电解水制氢系
统的电解水过程,可以使用碱性电解槽中可逆电压的数值模型来描述可逆电压与温度和压
力之间的关系,建立碱性电解槽欧姆过电位的数值模型描述隔膜阻抗、电解液阻抗、气泡、
阻抗、电极材料阻抗的关系。
[0067] 例如:需要建立一个运行环境模型,来模拟碱性电解水制氢系统的环境影响过程,
将电解槽等效电阻抗基于电路理论的数学表达式转换为仅与温度相关的数学特征表达式,
基于阻抗泛化模型,以槽温为唯一变量推导电解槽经济额定功率,作为电解槽运行功率上
8
CN 117252032 B
说明书
5/11页
限;以电化学反应热平衡方程为基础,得到电-热模型数学方程,进而推导电解槽保温功率
仅与温度相关的模型,作为电解槽运行功率下限。再利用电-热模型数学方程对时间积分,
得到温升特性模型,将所有以温度为唯一变量的数字孪生模型与温升模型联立,对时间分
段简化后得到升温过程中的功率调节模型,以等效电阻抗随温度的变化为基础得到电解电
流,进一步推得产氢特性模型。
[0068] 例如:需要建立一个碱性电解水制氢系统的动态模型,包含电解槽溫升特性建模,
可用电解槽温升特性模型能推导出不同的工作环境中电解槽维持电解反应的最小电解电
流,计算得到电解槽在不同工作环境下的保温功率;还包含电解槽功率调节特性建模,明确
电解槽的功率调节特性使电解槽的输出功率稳定在安全区间内;电解槽产氢特性建模,以
电解槽正常运行阶段等效电阻抗随温度的变化公式为基础,结合正常运行阶段的伏安特
性,再根据电解反应中的电荷守恒及法拉第电解定律,即可推导出产氢量;电解槽分离罐压
强建模,对侧放的圆柱形容器液面高度与液体体积的函数关系进行近似处理,得到电解槽
工作过程中气液分离罐內的液面高度,通过理想气体状态方程得到气液分离罐內的压强。
[0069] 可见,本发明通过构建机理模型、运行环境模型以及动态模型模拟实际碱性电解
水制氢系统的工作原理、工作过程、实际性能等,并综合考虑工况条件和边界条件等因素来
提高系统的可靠性。提高了数字孪生体的准确性、灵活性以及稳定性。
[0070] 本发明示例性的提供了一种步骤3所述基于所述仿真模型,得到初始数字孪生体
的方法,包括:
[0071] 步骤3.1 整合所述机理模型、所述运行环境模型以及所述动态模型,得到所述模
拟碱性电解水制氢系统;
[0072] 步骤3.2 存储所述模拟碱性电解水制氢系统的系统状态参数;例如:系统状态、参
数等。
[0073] 步骤3.3 基于所述运行环境模型,生成用于调整所述系统状态参数和所述控制策
略的自适应控制算法;例如:根据运行环境模型中的PID控制方法,生成自适应控制算法,实
现数字孪生体的自适应控制,如模型预测控制等,用于自动调整数字孪生体中的系统参数
和控制策略。
[0074] 步骤3.4 整合所述模拟碱性电解水制氢系统、存储的系统状态参数以及自适应控
制算法,得到所述初始数字孪生体。
[0075] 在本发明中,通过自适应控制算法来提高系统的自适应性,通过自适应控制算法
来提高系统的灵活性,提高碱性电解水制氢系统的效率和可靠性,提高经济效益。
[0076] 本发明示例性的提供了一种优化算法,包括:深度学习、人工神经网络、遗传算法、
粒子群算法中的一种。通过预设优化算法,对所述机理模型、运行环境模型以及动态模型进
行优化,提高系统性能。
[0077] 本发明示例性的提供了一种步骤4所述对所述初始数字孪生体进行测试和调整,
直到所述初始数字孪生体满足所述目标性能的方法,包括:
[0078] 步骤4.1 从所述实际碱性电解水制氢系统获取实际测试数据,并将所述实际测试
数据导入所述初始数字孪生体;
[0079]
步骤4.2 根据预设监测算法,对所述初始数字孪生体中的模拟碱性电解水制氢系
统根据所述实际测试数据进行运行的过程进行实时监测,得到实时数据;
9
CN 117252032 B
说明书
6/11页
[0080] 步骤4.3 对所述实时数据进行分析,判断所述模拟碱性电解水制氢系统是否存在
问题;
[0081] 步骤4.4 在所述模拟碱性电解水制氢系统不存在问题的情况下,利用预设仿真模
型和所述实际碱性电解水制氢系统的实际运行数据验证所述模拟碱性电解水制氢系统的
系统性能是否符合预设条件;
[0082] 步骤4.5 在所述系统性能符合所述预设条件的情况下,对所述初始数字孪生体进
行多次测试和验证,并根据测试结果和验证结果调整所述初始数字孪生,直到所述初始数
字孪生体满足所述目标性能。
[0083] 本发明将初始数字孪生体与实际碱性电解水制氢系统进行数据对接,从实际碱性
电解水制氢系统获取实际测试数据,将实际测试数据导入初始数字孪生体。利用智能检测
算法(即预设监测算法),如机器视觉、传感器网络等,对初始数字孪生体中的模拟碱性电解
水制氢系统进行实时监测,得到表示其系统状态的实时数据。对实时数据进行大数据分析,
大数据分析算法包括数据挖掘、机器学习等,用于分析模拟碱性电解水制氢系统的运行数
据,进而判断模拟碱性电解水制氢系统是否存在问题,如果有问题则进行解决。如果没问
题,利用预设仿真模型和实际碱性电解水制氢系统的实际运行数据验证模拟碱性电解水制
氢系统的系统性能是否符合预设条件,其中,预设条件为模拟碱性电解水制氢系统的系统
性能满足需求,且该模拟碱性电解水制氢系统具有可行性。在模拟碱性电解水制氢系统符
合该预设条件的情况下,根据仿真过程中初始数字孪生体的运行情况进行验证,并对初始
数字孪生体进行优化调整,如调整控制策略、优化系统参数,确保初始数字孪生体的可靠性
和稳定性。
[0084] 本发明在实际实现过程中,考虑到环境因素对系统性能的影响,可及时调整系统
参数以保证系统性能。另外,可以根据测试结果和验证结果并对模拟碱性电解水制氢系统
和实际碱性电解水制氢系统进行细节完善,如确定系统结构、设计零部件等。
[0085] 本发明通过智能检测,实现智能监测算法(即预设监测算法),对系统状态进行实
时监测,提高系统的安全性。
[0086] 本发明通过大数据分析,对系统数据进行分析,发现系统问题并进行解决(即判断
模拟碱性电解水制氢系统是否存在问题)。本发明通过引入大数据分析与智能算法,从碱性
电解水制氢系统应用实际出发,充分考虑数字孪生体需要集成关联的各种数学分析模型,
建立更贴近实际场景需要、可动态更新仿真计算的碱性电解水制氢系统系统数字孪生体,
从而通过大数据分析方法来提高系统的数据分析能力。
[0087] 本发明通过仿真和实验,利用仿真模型(即预设仿真模型)验证系统性能,进行实
验验证系统的可行性。例如:使用MATLAB/Simulink,Ansys等软件工具来进行建模和仿真,
在仿真过程中,需要考虑到时间延迟对初始数字孪生中的系统性能的影响。通过对比实际
碱性电解水制氢系统和初始数字孪生体的性能进行验证,并使用实际碱性电解水制氢系统
的数据对初始数字孪生体进行校验,确保其准确性和可靠性。实现通过精确分析系统性能
来提高系统效率,并通过提高系统的效率和可靠性来提高经济效益。
[0088] 另外,初始数字孪生体和数字孪生体还包括:可视化模型,三维可视化展示用的高
精度数字化模型;仿真模型,包含系统的仿真模型,用于模拟系统的运行情况和验证系统性
能。
10
CN 117252032 B
说明书
7/11页
[0089] 本发明示例性的提供了一种步骤4.5所述根据测试结果和验证结果调整所述初始
数字孪生的方法,包括:
[0090] 步骤4.5.1 根据所述测试结果和所述验证结果,确定所述初始数字孪生体的运行
情况;
[0091] 步骤4.5.2 根据所述运行情况调整所述控制策略和优化所述系统状态参数。
[0092] 本发明通过上述方法实现根据测试结果和验证结果对初始数字孪生体进行调整,
确保数字孪生体能够满足需求并达到目标性能。
[0093] 在对实时数据进行分析,判断模拟碱性电解水制氢系统是否存在问题之后,本发
明示例性的提供了一种智能控制方法,包括:在模拟碱性电解水制氢系统不存在问题的情
况下,确定预设控制算法,所述初始数字孪生体通过预设控制算法控制实际碱性电解水制
氢系统的运行。
[0094] 该方法采用智能控制算法(即预设控制算法),提高控制策略的灵活性和智能性,
数字孪生体可以通过该智能控制算法控制实际碱性电解水制氢系统。从而通过智能监测和
智能控制算法来提高碱性电解水制氢系统的智能性。
[0095] 在将初始数字孪生体作为数字孪生体之后,本发明示例性的提供了一种方法,包
括:按照预设周期对数字孪生体进行维护和升级。
[0096] 例如:在实际实现过程中,考虑到环境因素对系统性能的影响,通过外部接口及时
调整数字孪生体的系统参数以保证数字孪生体的性能,其中:
[0097] 所述外部接口,包含:系统维护和升级的接口,可以方便的进行系统维护和升级操
作。
[0098]
维护和升级:定期(即按照预设周期)对数字孪生体进行维护和升级操作,保证数
字孪生体总能保持高效,精准和可靠。
[0099] 本发明通过定期对数字孪生体进行维护和升级,保证了数字孪生体的准确性和可
靠性、降低能源消耗,从而提高碱性电解水制氢系统的可持续性。
[0100] 本发明示例性的提供了一种碱性电解水制氢系统的数字孪生体构建装置,该装置
包括:
[0101] 获取模块,用于获取实际碱性电解水制氢系统的系统参数和数字孪生体的目标性
能,其中,所述系统参数能够得到所述目标性能;
[0102]
建立模块,用于根据所述系统参数,建立多个仿真模型;
[0103] 得到模块,用于基于所述仿真模型,得到初始数字孪生体;
[0104]
调整模块,用于将所述初始数字孪生体与所述实际碱性电解水制氢系统进行数据
对接,对所述初始数字孪生体进行测试和调整,直到所述初始数字孪生体满足所述目标性
能;
[0105] 作为模块,用于将所述初始数字孪生体作为数字孪生体。
[0106] 其中,建立模块包括:
[0107] 第一获取单元,用于从所述系统参数中获取所述实际碱性电解水制氢系统中的工
况参数;
[0108] 第一得到单元,用于基于所述工况参数,并通过预设方程描述所述碱性电解槽运
行过程,得到模拟碱性电解水制氢系统的机理模型,其中,所述模拟碱性电解水制氢系统包
11
CN 117252032 B
含于所述初始数字孪生体;
说明书
8/11页
[0109] 第一确定单元,用于根据所述工况参数确定所述模拟碱性电解水制氢系统的控制
策略;
[0110] 第一建立单元,用于根据所述控制策略、所述碱性电解槽运行过程以及预设控制
方法,建立所述模拟碱性电解水制氢系统的运行环境模型;
[0111] 第二获取单元,用于从所述系统参数中获取所述碱性电解水制氢系统中的边界参
数;
[0112] 第二建立单元,用于根据所述边界参数,建立所述模拟碱性电解水制氢系统的动
态模型。
[0113] 得到模块包括:
[0114] 优化单元,用于通过预设优化算法,对所述机理模型、所述运行环境模型以及所述
动态模型进行优化;
[0115] 第一整合单元,用于整合所述机理模型、所述运行环境模型以及所述动态模型,得
到所述模拟碱性电解水制氢系统;
[0116] 第三建立单元,用于建立数据库,其中,所述数据库用于存储所述模拟碱性电解水
制氢系统的系统状态参数;
[0117] 生成单元,用于基于所述运行环境模型,生成自适应控制算法,其中,所述自适应
控制算法用于调整所述系统状态参数和所述控制策略;
[0118] 第二整合单元,用于整合所述模拟碱性电解水制氢系统、所述数据库以及所述自
适应控制算法,得到所述初始数字孪生体。
[0119] 调整模块包括:
[0120] 第三获取单元,用于从所述实际碱性电解水制氢系统获取实际测试数据,并将所
述实际测试数据导入所述初始数字孪生体;
[0121] 监测单元,用于根据预设监测算法,对所述初始数字孪生体中的所述模拟碱性电
解水制氢系统进行实时监测,得到实时数据;
[0122] 判断单元,用于对所述实时数据进行分析,判断所述模拟碱性电解水制氢系统是
否存在问题;
[0123] 验证单元,用于在所述模拟碱性电解水制氢系统不存在问题的情况下,利用预设
仿真模型和所述实际碱性电解水制氢系统的实际运行数据验证所述模拟碱性电解水制氢
系统的系统性能是否符合预设条件;
[0124] 调整单元,用于在所述系统性能符合所述预设条件的情况下,对所述初始数字孪
生体进行多次测试和验证,并根据测试结果和验证结果调整所述初始数字孪生,直到所述
初始数字孪生体满足所述目标性能。
[0125]
调整单元包括:
[0126] 确定子模块,用于根据所述测试结果和所述验证结果,确定所述初始数字孪生体
的运行情况;
[0127] 调整子模块,用于根据所述运行情况调整所述控制策略和优化所述系统状态参
数。
[0128] 调整模块还包括:
12
CN 117252032 B
说明书
9/11页
[0129] 第二确定单元,用于在所述模拟碱性电解水制氢系统不存在问题的情况下,确定
预设控制算法,其中,所述初始数字孪生体通过所述预设控制算法控制所述实际碱性电解
水制氢系统。
[0130] 本发明示例性的提供了一种碱性电解水制氢系统的数字孪生体构建装置,在上述
碱性电解水制氢系统的数字孪生体构建装置的基础上,还包括:
[0131] 升级模块,用于按照预设周期对所述数字孪生体进行维护和升级。
[0132] 本发明示例性的提供了一种碱性电解水制氢系统的数字孪生体构建装置,在上述
碱性电解水制氢系统的数字孪生体构建装置的基础上,还包括:
[0133] 控制模块,用于通过预设控制算法控制实际碱性电解水制氢系统的运行。
[0134] 本发明示例性的提供了一种设备,该电子设备可以是服务器、终端、或者其组合。
该设备包括:处理器、通信接口、存储器和通信总线,其中,处理器、通信接口和存储器通过
通信总线完成相互间的通信;其中,存储器,用于存储计算机程序;处理器,用于通过运行所
述存储器上所存储的所述计算机程序来执行上述碱性电解水制氢系统的数字孪生体构建
方法中的方法步骤。
[0135] 其中:上述的通信总线可以是PCI (Peripheral Component Interconnect,外设部
件互连标准)总线、或EISA(Extended Industry Standard Architecture,扩展工业标准结
构)总线等。该通信总线可以分为地址总线、数据总线、控制总线等。通信接口用于上述电子
设备与其他设备之间的通信。存储器可以包括RAM,也可以包括非易失性存储器(non-
volatile memory),例如,至少一个磁盘存储器。可选地,存储器还可以是至少一个位于远
离前述处理器的存储装置。上述处理器可以是通用处理器,可以包含但不限于:CPU
(Central Processing Unit,中央处理器)、NP (Network Processor,网络处理器)等;还可
以是DSP(Digital Signal Processing,数字信号处理器)、ASIC (Application Specific
Integrated Circuit,专用集成电路)、FPGA(Field-Programmable Gate Array,现场可
编程门阵列)或者其他可编程逻辑器件、分立门或者晶体管逻辑器件、分立硬件组件。实施
上述碱性电解水制氢系统的数字孪生体构建方法的设备可以是终端设备,该终端设备可以
是智能手机(如Android手机、iOS手机等)、平板电脑、掌上电脑以及移动互联网设备
(Mobile Internet Devices,MID)、PAD等终端设备。
[0136] 本发明示例性的提供了一种计算机可读的存储介质,该存储介质中存储有计算机
程序,该计算机程序被设置为运行时执行上述碱性电解水制氢系统的数字孪生体构建方法
中的方法步骤。
[0137]
本发明示例的计算机可读的存储介质可以是一种位于网络中的多个网络设备中
的至少一个网络设备上。也可以是:U盘、ROM、RAM、移动硬盘、磁碟或者光盘等各种可以存储
计算机程序的介质。
[0138] 以下结合具体实施例对本发明进行进一步阐释。
实施例1
[0139] 使用数字孪生体分析碱性电解水的直流能耗时,数字孪生体的构建和使用步骤包
括:
[0140] 步骤1:建立碱性电解水制氢系统数字孪生体模型,模型应包含系统结构、材料、工
13
CN 117252032 B
艺、环境条件等因素。
[0141]
说明书
10/11页
步骤2:录入系统的原始数据,如材料性能数据、环境条件、工艺参数、电能数据等。
[0142] 步骤3:利用数字孪生体模拟碱性电解水制氢系统在不同时间段内的运行状态。
[0143] 步骤4:分析模拟结果,确定系统在不同时间段内的直流能耗。
[0144] 步骤5:通过对比不同时间段内系统的直流能耗,得出系统的直流能耗。
[0145] 步骤6:通过模拟结果和直流能耗,确定系统更换和维护的时间计划。
[0146] 步骤7:不断更新数字孪生体模型,通过对实际系统运行数据的更新,来保证预测
结果的准确性。
[0147] 在本申请实施例中,构建数字孪生体,并利用数字孪生体模拟碱性电解水制氢系
统在不同时间段内的运行状态,并根据模拟结果确定直流能耗。提高了碱性电解水制氢系
统的效率和安全性。
实施例2
[0148] 使用数字孪生体对碱性电解水制氢系统进行故障预警时,数字孪生体的构建和使
用步骤包括:
[0149] 步骤1:建立碱性电解水制氢系统数字孪生体模型,模型应包含系统结构、材料、工
艺、环境条件等因素。
[0150]
步骤2:录入系统的原始数据,如材料性能数据、环境条件、工艺参数等。
[0151] 步骤3:利用数字孪生体模拟碱性电解水制氢系统在不同时间段内的运行状态。
[0152] 步骤4:分析模拟结果,确定系统在不同时间段内的故障可能性。
[0153]
步骤5:利用数字孪生体模拟碱性电解水制氢系统在不同环境条件下的性能,预测
系统在不同环境条件下的故障可能性。
[0154]
步骤6:通过对比不同时间段内系统的故障可能性,得出系统的预警信号。
[0155] 步骤7:利用预警信号进行系统的维护和检修。
[0156] 步骤8:不断地监测和记录系统运行状态,并不断根据新数据更新数字孪生体模
型。
[0157] 步骤9:利用数字孪生体对系统进行实时监控,及时发现并处理可能出现的问题。
[0158]
步骤10:通过不断更新数字孪生体模型和监测系统运行状态,不断优化系统的使
用寿命预测和故障预警。
[0159] 在本申请实施例中,通过构建数字孪生体,并利用数字孪生体模拟碱性电解水制
氢系统在不同时间段内的运行状态,并分析模拟结果,得出故障可能性,并发出预警信号。
提高了碱性电解水制氢系统的效率和安全性。
实施例3
[0160] 使用碱性电解水制氢系统数字孪生体用于电解槽控制参数的优化时,数字孪生体
的构建和使用步骤包括:
[0161] 步骤1:使用碱性电解水制氢系统数字孪生体对控制系统进行评估,确定系统的电
能利用情况。
[0162] 步骤2:利用数字孪生体对控制系统进行建模,通过仿真预测在不同时间段内的电
14
CN 117252032 B
能需求和供应情况。
说明书
11/11页
[0163] 步骤3:根据仿真结果,制定控制策略,并进行输入电压、电流或电解槽温度调整。
[0164] 步骤4:使用碱性电解水制氢系统数字孪生体对制氢过程进行监控和优化,确保系
统的最佳运行状态。
[0165] 步骤5:利用数字孪生体对能源消耗进行监测和优化,以降低能源成本。
[0166] 步骤6:通过对能源管理系统进行数字化建模和仿真,提高系统的运行效率和稳定
性。
[0167] 步骤7:利用碱性电解水制氢系统数字孪生体进行预测性维护和故障诊断,确保系
统的高可用性。
[0168] 步骤8:利用数字孪生体对设备进行远程监控和维护,提高设备的使用寿命。
[0169] 步骤9:通过数字孪生体对控制系统的数据进行分析,提高系统的智能化水平。
[0170] 在本申请实施例中,通过构建数字孪生体,并利用数字孪生体模拟碱性电解水制
氢系统在不同时间段内的运行状态,并分析模拟结果,确定电能需求和供应情况,进行控制
决策。提高了碱性电解水制氢系统的智能化水平、运行效率以及稳定性。
[0171] 以上述依据本发明的理想实施例为启示,通过上述的说明内容,相关工作人员完
全可以在不偏离本项发明技术思想的范围内,进行多样的变更以及修改。本项发明的技术
性范围并不局限于说明书上的內容,必须要根据权利要求范围来确定其技术性范围。
15
