#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新插入新内容后的段落编号
"""

def update_numbers_after_insertion(file_path):
    """更新插入新内容后的段落编号"""
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 定义需要更新的编号映射（从[0042]开始需要更新）
    number_mapping = {
        '[0042]': '[0062]',
        '[0043]': '[0063]',
        '[0044]': '[0064]',
        '[0045]': '[0065]',
        '[0046]': '[0066]',
        '[0047]': '[0067]',
        '[0048]': '[0068]',
        '[0049]': '[0069]',
        '[0050]': '[0070]',
        '[0051]': '[0071]',
        '[0052]': '[0072]',
        '[0053]': '[0073]',
        '[0054]': '[0074]',
        '[0055]': '[0075]',
        '[0056]': '[0076]',
        '[0057]': '[0077]',
        '[0058]': '[0078]',
        '[0059]': '[0079]',
        '[0060]': '[0080]',
        '[0061]': '[0081]',
        '[0062]': '[0082]',
        '[0063]': '[0083]',
        '[0064]': '[0084]',
        '[0065]': '[0085]',
        '[0066]': '[0086]',
        '[0067]': '[0087]',
        '[0068]': '[0088]',
        '[0069]': '[0089]',
        '[0070]': '[0090]',
        '[0071]': '[0091]',
        '[0072]': '[0092]',
        '[0073]': '[0093]',
        '[0074]': '[0094]',
        '[0075]': '[0095]',
        '[0076]': '[0096]',
        '[0077]': '[0097]',
        '[0078]': '[0098]',
        '[0079]': '[0099]',
        '[0080]': '[0100]',
        '[0081]': '[0101]',
        '[0082]': '[0102]',
        '[0083]': '[0103]',
        '[0084]': '[0104]',
        '[0085]': '[0105]',
        '[0086]': '[0106]',
        '[0087]': '[0107]',
        '[0088]': '[0108]',
        '[0089]': '[0109]',
        '[0090]': '[0110]',
        '[0091]': '[0111]',
        '[0092]': '[0112]',
        '[0093]': '[0113]',
        '[0094]': '[0114]',
        '[0095]': '[0115]',
        '[0096]': '[0116]',
        '[0097]': '[0117]',
        '[0098]': '[0118]',
        '[0099]': '[0119]',
        '[0100]': '[0120]',
        '[0101]': '[0121]',
        '[0102]': '[0122]',
        '[0103]': '[0123]'
    }
    
    # 按照映射替换编号
    for old_num, new_num in number_mapping.items():
        content = content.replace(old_num, new_num)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已更新段落编号: {file_path}")

if __name__ == "__main__":
    file_path = "专利方向贮备/4-绿电制氨、掺氨燃烧系统数字孪生/相关专利申请书/一种基于数字孪生技术的绿电制氨掺氨燃烧全链路智能协同控制系统.txt"
    update_numbers_after_insertion(file_path)