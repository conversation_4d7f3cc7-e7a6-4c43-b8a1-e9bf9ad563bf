#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF论文分析器 - 专门用于分析学术论文PDF
"""

import sys
from pathlib import Path

def analyze_pdf_paper(pdf_path):
    """分析PDF论文内容"""
    try:
        # 尝试使用PyPDF2
        try:
            import PyPDF2
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                return extract_paper_info(text, "PyPDF2")
        except ImportError:
            print("PyPDF2 not available, trying pdfplumber...")
        
        # 尝试使用pdfplumber
        try:
            import pdfplumber
            text = ""
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
                return extract_paper_info(text, "pdfplumber")
        except ImportError:
            print("pdfplumber not available, trying pymupdf...")
        
        # 尝试使用pymupdf (fitz)
        try:
            import fitz
            doc = fitz.open(pdf_path)
            text = ""
            for page in doc:
                text += page.get_text() + "\n"
            doc.close()
            return extract_paper_info(text, "pymupdf")
        except ImportError:
            print("pymupdf not available")
        
        return "无法提取PDF内容，请安装 PyPDF2, pdfplumber 或 pymupdf"
        
    except Exception as e:
        return f"PDF处理错误: {e}"

def extract_paper_info(text, method):
    """从文本中提取论文关键信息"""
    if not text.strip():
        return f"使用{method}提取的文本为空"
    
    lines = text.split('\n')
    result = []
    
    result.append(f"=== PDF内容分析 (使用{method}) ===\n")
    result.append(f"总字符数: {len(text)}")
    result.append(f"总行数: {len(lines)}\n")
    
    # 尝试提取标题
    title = extract_title(lines)
    if title:
        result.append(f"论文标题: {title}\n")
    
    # 尝试提取作者
    authors = extract_authors(lines)
    if authors:
        result.append(f"作者: {authors}\n")
    
    # 尝试提取摘要
    abstract = extract_abstract(text)
    if abstract:
        result.append(f"摘要:\n{abstract}\n")
    
    # 尝试提取关键词
    keywords = extract_keywords(text)
    if keywords:
        result.append(f"关键词: {keywords}\n")
    
    # 提取章节标题
    sections = extract_sections(lines)
    if sections:
        result.append("主要章节:")
        for section in sections[:10]:  # 只显示前10个章节
            result.append(f"  - {section}")
        result.append("")
    
    # 显示前500个字符的内容预览
    preview = text[:500].replace('\n', ' ').strip()
    result.append(f"内容预览:\n{preview}...\n")
    
    return '\n'.join(result)

def extract_title(lines):
    """提取论文标题"""
    for i, line in enumerate(lines[:20]):  # 在前20行中查找
        line = line.strip()
        if len(line) > 10 and len(line) < 200:
            # 标题通常不会太短或太长
            if not line.lower().startswith(('abstract', 'introduction', 'keywords')):
                if any(word in line.lower() for word in ['ammonia', 'digital', 'twin', 'combustion', 'green']):
                    return line
    return None

def extract_authors(lines):
    """提取作者信息"""
    for i, line in enumerate(lines[:30]):
        line = line.strip()
        if any(indicator in line.lower() for indicator in ['author', 'by ']):
            return line
        # 查找包含多个大写字母的行（可能是作者名）
        if len(line) > 5 and len(line) < 100:
            words = line.split()
            if len(words) >= 2 and sum(1 for w in words if w[0].isupper()) >= 2:
                return line
    return None

def extract_abstract(text):
    """提取摘要"""
    text_lower = text.lower()
    abstract_start = text_lower.find('abstract')
    if abstract_start != -1:
        # 查找摘要结束位置
        possible_ends = ['keywords', 'introduction', '1. introduction', 'key words']
        abstract_end = len(text)
        for end_marker in possible_ends:
            pos = text_lower.find(end_marker, abstract_start + 8)
            if pos != -1 and pos < abstract_end:
                abstract_end = pos
        
        abstract = text[abstract_start:abstract_end].strip()
        # 清理摘要文本
        abstract = abstract.replace('Abstract', '').replace('ABSTRACT', '').strip()
        if len(abstract) > 50 and len(abstract) < 2000:
            return abstract
    return None

def extract_keywords(text):
    """提取关键词"""
    text_lower = text.lower()
    keywords_start = text_lower.find('keywords')
    if keywords_start == -1:
        keywords_start = text_lower.find('key words')
    
    if keywords_start != -1:
        # 查找关键词结束位置
        keywords_end = text_lower.find('\n\n', keywords_start)
        if keywords_end == -1:
            keywords_end = keywords_start + 200
        
        keywords = text[keywords_start:keywords_end].strip()
        keywords = keywords.replace('Keywords:', '').replace('Key words:', '').strip()
        if len(keywords) > 10 and len(keywords) < 300:
            return keywords
    return None

def extract_sections(lines):
    """提取章节标题"""
    sections = []
    for line in lines:
        line = line.strip()
        # 查找数字开头的章节
        if len(line) > 3 and len(line) < 100:
            if line[0].isdigit() and '.' in line[:5]:
                sections.append(line)
            # 查找全大写的章节标题
            elif line.isupper() and len(line.split()) <= 5:
                sections.append(line)
    return sections

if __name__ == "__main__":
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
        if Path(pdf_path).exists():
            result = analyze_pdf_paper(pdf_path)
            print(result)
        else:
            print("PDF文件不存在")
    else:
        print("请提供PDF文件路径")