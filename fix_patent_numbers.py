#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正专利申请书段落编号的脚本
"""

import re

def fix_patent_numbers(file_path):
    """修正专利申请书的段落编号"""
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 手动修正编号映射
    number_mapping = {
        # 发明内容部分 - 从[0018]开始改为从[0011]开始
        '[0018]': '[0011]',
        '[0019]': '[0012]',
        '[0019a]': '[0012a]',
        '[0019b]': '[0012b]',
        '[0020]': '[0013]',
        '[0014]': '[0014]',  # 这个位置不对，应该在[0020]之后
        '[0015]': '[0015]',  # 这个位置不对，应该在[0014]之后
        '[0016]': '[0016]',  # 这个位置不对，应该在[0015]之后
        '[0017]': '[0017]',  # 这个位置不对，应该在[0016]之后
        '[0018]': '[0018]',  # 重复了
        '[0018a]': '[0018a]',
        '[0018b]': '[0018b]',
        '[0019]': '[0019]',  # 重复了
        '[0021]': '[0020]',
        '[0022]': '[0021]',
        '[0022a]': '[0021a]',
        '[0022b]': '[0021b]',
        '[0023]': '[0022]',
        '[0024]': '[0023]',
        '[0025]': '[0024]',
        '[0025a]': '[0024a]',
        '[0025b]': '[0024b]',
        '[0026]': '[0025]',
        '[0026a]': '[0025a]',
        '[0026b]': '[0025b]',
        '[0026c]': '[0025c]',
        '[0027]': '[0026]',
        '[0028]': '[0027]',
        
        # 附图说明部分 - 从[0035]开始改为从[0028]开始
        '[0035]': '[0028]',
        '[0036]': '[0029]',
        '[0037]': '[0030]',
        '[0038]': '[0031]',
        '[0039]': '[0032]',
        
        # 具体实施方式部分 - 从[0040]开始改为从[0033]开始
        '[0040]': '[0033]',
        '[0041]': '[0034]',
        '[0042]': '[0035]',
        '[0043]': '[0036]',
        '[0044]': '[0037]',
        '[0045]': '[0038]',
        '[0046]': '[0039]',
        '[0047]': '[0040]',
        '[0048]': '[0041]',
        '[0049]': '[0042]',
        '[0050]': '[0043]',
        '[0051]': '[0044]',
        '[0052]': '[0045]',
        '[0053]': '[0046]',
        '[0054]': '[0047]',
        '[0055]': '[0048]',
        '[0056]': '[0049]',
        '[0057]': '[0050]',
        '[0058]': '[0051]',
        '[0059]': '[0052]',
        '[0060]': '[0053]',
        '[0061]': '[0054]',
        '[0062]': '[0055]',
        '[0063]': '[0056]',
        '[0064]': '[0057]',
        '[0065]': '[0058]',
        '[0066]': '[0059]',
        '[0067]': '[0060]',
        '[0068]': '[0061]',
        '[0069]': '[0062]',
        '[0070]': '[0063]',
        '[0071]': '[0064]',
        '[0072]': '[0065]',
        '[0073]': '[0066]',
        '[0074]': '[0067]',
        '[0075]': '[0068]',
        '[0076]': '[0069]',
        '[0077]': '[0070]',
        '[0078]': '[0071]',
        '[0079]': '[0072]',
        '[0080]': '[0073]',
        '[0081]': '[0074]',
        '[0082]': '[0075]',
        '[0083]': '[0076]',
        '[0084]': '[0077]',
        '[0085]': '[0078]',
        '[0086]': '[0079]',
        '[0087]': '[0080]',
        '[0088]': '[0081]',
        '[0089]': '[0082]',
        '[0090]': '[0083]'
    }
    
    # 先处理错位的段落 - 将[0014]-[0017]移动到正确位置
    # 这需要重新组织内容结构
    
    # 按照映射替换编号
    for old_num, new_num in number_mapping.items():
        content = content.replace(old_num, new_num)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已修正文件: {file_path}")

if __name__ == "__main__":
    file_path = "专利方向贮备/4-绿电制氨、掺氨燃烧系统数字孪生/相关专利申请书/一种基于数字孪生技术的绿电制氨掺氨燃烧全链路智能协同控制系统.txt"
    fix_patent_numbers(file_path)