#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正专利申请书段落编号的脚本
"""

import re

def fix_paragraph_numbers(file_path):
    """修正段落编号"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 找到发明内容部分开始的位置
    invention_start = content.find('发明内容')
    if invention_start == -1:
        print("未找到发明内容部分")
        return
    
    # 从发明内容开始的部分
    before_invention = content[:invention_start]
    after_invention = content[invention_start:]
    
    # 创建编号映射表（从发明内容开始重新编号）
    # 背景技术用到了[0010]，所以发明内容应该从[0011]开始
    number_mapping = {}
    
    # 发明内容部分的编号映射
    old_numbers = [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83]
    new_numbers = list(range(11, 11 + len(old_numbers)))
    
    for old, new in zip(old_numbers, new_numbers):
        number_mapping[old] = new
    
    # 替换编号
    def replace_number(match):
        old_num = int(match.group(1))
        if old_num in number_mapping:
            return f"[{number_mapping[old_num]:04d}]"
        return match.group(0)
    
    # 使用正则表达式替换编号
    after_invention = re.sub(r'\[(\d+)\]', replace_number, after_invention)
    
    # 处理带字母的编号（如[0005a]）
    def replace_letter_number(match):
        old_num = int(match.group(1))
        letter = match.group(2)
        if old_num in number_mapping:
            return f"[{number_mapping[old_num]:04d}{letter}]"
        return match.group(0)
    
    after_invention = re.sub(r'\[(\d+)([a-z])\]', replace_letter_number, after_invention)
    
    # 合并内容
    new_content = before_invention + after_invention
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("段落编号修正完成")

if __name__ == "__main__":
    file_path = "专利方向贮备/4-绿电制氨、掺氨燃烧系统数字孪生/相关专利申请书/一种基于数字孪生技术的绿电制氨掺氨燃烧全链路智能协同控制系统.txt"
    fix_paragraph_numbers(file_path)