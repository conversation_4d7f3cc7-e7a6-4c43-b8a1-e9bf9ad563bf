#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版PDF论文分析工具
不依赖外部库，基于文件名和基本信息进行分析

使用方法: py simple_analyzer.py
"""

import os
import re
from pathlib import Path
from datetime import datetime

def analyze_papers():
    """分析当前目录下的PDF论文"""
    current_dir = Path(__file__).parent
    pdf_files = list(current_dir.glob("*.PDF")) + list(current_dir.glob("*.pdf"))
    
    if not pdf_files:
        print("当前目录下没有找到PDF文件")
        return
    
    print("风光氢储能技术 - 中文论文分析报告")
    print("=" * 60)
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"分析路径: {current_dir}")
    print(f"发现论文数量: {len(pdf_files)}")
    print()
    
    # 去重处理
    unique_files = {}
    for pdf_file in pdf_files:
        file_key = pdf_file.name.lower()
        if file_key not in unique_files:
            unique_files[file_key] = pdf_file
    
    print(f"去重后论文数量: {len(unique_files)}")
    print()
    
    analysis_results = []
    
    for i, (file_key, pdf_file) in enumerate(unique_files.items(), 1):
        print(f"论文 {i}: {pdf_file.name}")
        print("-" * 50)
        
        # 基本信息
        file_size_mb = pdf_file.stat().st_size / 1024 / 1024
        print(f"文件大小: {file_size_mb:.2f} MB")
        
        # 分析文件名
        analysis = analyze_filename(pdf_file.name)
        
        print(f"论文类型: {analysis['type']}")
        print(f"研究主题: {analysis['topic']}")
        
        if analysis['keywords']:
            print(f"技术关键词: {', '.join(analysis['keywords'])}")
        
        # 技术领域分析
        tech_areas = analyze_technical_areas(pdf_file.name)
        if tech_areas:
            print(f"技术领域: {', '.join(tech_areas)}")
        
        # 创新点预测
        innovation_points = predict_innovation_points(analysis)
        if innovation_points:
            print("可能的创新点:")
            for point in innovation_points:
                print(f"  • {point}")
        
        # 专利申请价值评估
        patent_value = assess_patent_value(analysis)
        print(f"专利申请参考价值: {patent_value['level']} - {patent_value['reason']}")
        
        analysis_results.append({
            'file': pdf_file.name,
            'analysis': analysis,
            'tech_areas': tech_areas,
            'innovation_points': innovation_points,
            'patent_value': patent_value,
            'file_size_mb': file_size_mb
        })
        
        print()
    
    # 生成综合分析报告
    generate_summary_report(analysis_results, current_dir)
    
    return analysis_results

def analyze_filename(filename):
    """从文件名分析论文信息"""
    analysis = {
        'type': '未知类型',
        'topic': '',
        'keywords': [],
        'degree_level': '',
        'research_method': []
    }
    
    # 判断论文类型和学位级别
    if '博士论文' in filename or '博士' in filename:
        analysis['type'] = '博士学位论文'
        analysis['degree_level'] = '博士'
    elif '硕士论文' in filename or '硕士' in filename:
        analysis['type'] = '硕士学位论文'
        analysis['degree_level'] = '硕士'
    elif '期刊' in filename:
        analysis['type'] = '期刊论文'
    
    # 提取研究方法
    methods = []
    if '模型预测控制' in filename or 'MPC' in filename:
        methods.append('模型预测控制')
    if '优化' in filename:
        methods.append('优化算法')
    if '仿真' in filename:
        methods.append('仿真分析')
    if '实验' in filename:
        methods.append('实验研究')
    
    analysis['research_method'] = methods
    
    # 提取技术关键词
    keywords = []
    keyword_mapping = {
        '风光氢': ['风光氢系统', '可再生能源制氢'],
        '储能': ['储能技术', '能量存储'],
        '优化配置': ['系统优化', '配置优化'],
        '协调控制': ['控制策略', '协调控制'],
        '模型预测控制': ['MPC', '预测控制'],
        '功率调控': ['功率管理', '功率控制'],
        '耦合系统': ['系统耦合', '多能互补'],
        '综合能源': ['综合能源系统', '多能源系统']
    }
    
    for key, values in keyword_mapping.items():
        if key in filename:
            keywords.extend(values)
    
    analysis['keywords'] = list(set(keywords))  # 去重
    
    # 提取主题
    topic = filename.replace('.PDF', '').replace('.pdf', '')
    topic = re.sub(r'\[.*?\]', '', topic)  # 去掉方括号内容
    analysis['topic'] = topic.strip()
    
    return analysis

def analyze_technical_areas(filename):
    """分析技术领域"""
    areas = []
    
    area_keywords = {
        '可再生能源': ['风光', '风电', '光伏', '太阳能', '风力'],
        '氢能技术': ['氢', '制氢', '电解水', '燃料电池'],
        '储能技术': ['储能', '储存', '蓄能'],
        '控制理论': ['控制', '调控', '管理', 'MPC', '预测控制'],
        '系统工程': ['系统', '集成', '优化', '配置'],
        '电力系统': ['电力', '电网', '功率', '电能'],
        '能源管理': ['能源管理', '能量管理', '调度']
    }
    
    for area, keywords in area_keywords.items():
        if any(keyword in filename for keyword in keywords):
            areas.append(area)
    
    return areas

def predict_innovation_points(analysis):
    """基于分析结果预测可能的创新点"""
    innovation_points = []
    
    keywords = analysis.get('keywords', [])
    methods = analysis.get('research_method', [])
    degree = analysis.get('degree_level', '')
    
    # 基于关键词预测创新点
    if '风光氢系统' in keywords:
        innovation_points.append('多能源系统集成优化方法')
        innovation_points.append('风光氢协调运行策略')
    
    if 'MPC' in keywords or '模型预测控制' in methods:
        innovation_points.append('先进控制算法在氢能系统中的应用')
        innovation_points.append('多约束条件下的预测控制策略')
    
    if '系统优化' in keywords:
        innovation_points.append('系统容量配置优化算法')
        innovation_points.append('多目标优化求解方法')
    
    if '功率管理' in keywords:
        innovation_points.append('智能功率分配策略')
        innovation_points.append('功率波动平抑技术')
    
    # 基于学位级别调整创新深度
    if degree == '博士':
        innovation_points.append('理论创新与方法突破')
        innovation_points.append('系统性解决方案')
    elif degree == '硕士':
        innovation_points.append('工程应用创新')
        innovation_points.append('算法改进与优化')
    
    return innovation_points

def assess_patent_value(analysis):
    """评估专利申请参考价值"""
    score = 0
    reasons = []
    
    # 技术新颖性评分
    if '模型预测控制' in analysis.get('research_method', []):
        score += 3
        reasons.append('采用先进控制理论')
    
    if len(analysis.get('keywords', [])) >= 4:
        score += 2
        reasons.append('技术覆盖面广')
    
    if analysis.get('degree_level') == '博士':
        score += 3
        reasons.append('研究深度较高')
    elif analysis.get('degree_level') == '硕士':
        score += 2
        reasons.append('工程应用性强')
    
    # 技术热点评分
    hot_keywords = ['风光氢', '储能', '优化', '智能控制']
    hot_count = sum(1 for keyword in analysis.get('keywords', []) 
                   if any(hot in keyword for hot in hot_keywords))
    score += hot_count
    
    if hot_count >= 2:
        reasons.append('涉及技术热点')
    
    # 评级
    if score >= 8:
        level = "高价值"
    elif score >= 5:
        level = "中等价值"
    else:
        level = "一般价值"
    
    return {
        'level': level,
        'score': score,
        'reason': '; '.join(reasons) if reasons else '基础研究'
    }

def generate_summary_report(results, output_dir):
    """生成综合分析报告"""
    report_file = output_dir / "论文分析综合报告.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("风光氢储能技术 - 中文论文综合分析报告\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"分析论文数量: {len(results)}\n\n")
        
        # 统计信息
        f.write("📊 统计信息\n")
        f.write("-" * 30 + "\n")
        
        degree_count = {}
        total_size = 0
        all_keywords = []
        all_tech_areas = []
        
        for result in results:
            degree = result['analysis'].get('degree_level', '未知')
            degree_count[degree] = degree_count.get(degree, 0) + 1
            total_size += result['file_size_mb']
            all_keywords.extend(result['analysis'].get('keywords', []))
            all_tech_areas.extend(result['tech_areas'])
        
        for degree, count in degree_count.items():
            f.write(f"{degree}论文: {count} 篇\n")
        
        f.write(f"总文件大小: {total_size:.2f} MB\n")
        f.write(f"平均文件大小: {total_size/len(results):.2f} MB\n\n")
        
        # 技术关键词统计
        f.write("🔑 技术关键词频次\n")
        f.write("-" * 30 + "\n")
        keyword_count = {}
        for keyword in all_keywords:
            keyword_count[keyword] = keyword_count.get(keyword, 0) + 1
        
        for keyword, count in sorted(keyword_count.items(), key=lambda x: x[1], reverse=True):
            f.write(f"{keyword}: {count} 次\n")
        f.write("\n")
        
        # 技术领域统计
        f.write("🎯 技术领域分布\n")
        f.write("-" * 30 + "\n")
        area_count = {}
        for area in all_tech_areas:
            area_count[area] = area_count.get(area, 0) + 1
        
        for area, count in sorted(area_count.items(), key=lambda x: x[1], reverse=True):
            f.write(f"{area}: {count} 篇论文涉及\n")
        f.write("\n")
        
        # 详细分析
        f.write("📋 详细分析结果\n")
        f.write("-" * 30 + "\n")
        
        for i, result in enumerate(results, 1):
            f.write(f"\n{i}. {result['file']}\n")
            f.write(f"   类型: {result['analysis']['type']}\n")
            f.write(f"   主题: {result['analysis']['topic']}\n")
            f.write(f"   关键词: {', '.join(result['analysis']['keywords'])}\n")
            f.write(f"   技术领域: {', '.join(result['tech_areas'])}\n")
            f.write(f"   专利价值: {result['patent_value']['level']}\n")
            
            if result['innovation_points']:
                f.write(f"   创新点:\n")
                for point in result['innovation_points']:
                    f.write(f"     • {point}\n")
        
        # 专利申请建议
        f.write("\n\n💡 专利申请建议\n")
        f.write("-" * 30 + "\n")
        
        high_value_papers = [r for r in results if r['patent_value']['level'] == '高价值']
        if high_value_papers:
            f.write("高价值论文重点关注:\n")
            for paper in high_value_papers:
                f.write(f"• {paper['file']}\n")
                f.write(f"  建议关注: {', '.join(paper['innovation_points'][:3])}\n")
        
        f.write("\n重点技术方向:\n")
        top_areas = sorted(area_count.items(), key=lambda x: x[1], reverse=True)[:3]
        for area, count in top_areas:
            f.write(f"• {area} (涉及{count}篇论文)\n")
        
        f.write("\n建议的专利申请方向:\n")
        f.write("• 风光氢多能源系统集成优化方法\n")
        f.write("• 基于模型预测控制的氢能系统控制策略\n")
        f.write("• 风光氢储能系统功率协调分配技术\n")
        f.write("• 可再生能源制氢系统优化配置方法\n")
    
    print(f"📄 综合分析报告已生成: {report_file.name}")

if __name__ == "__main__":
    try:
        results = analyze_papers()
        print("✅ 分析完成！")
        print("\n生成的文件:")
        print("• 论文分析综合报告.txt - 详细分析结果")
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()