#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修正所有段落编号
"""

def fix_final_numbers(file_path):
    """最终修正所有段落编号"""
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 定义需要修正的编号映射
    number_mapping = {
        # 附图说明部分应该从[0053]开始
        '[0053]': '[0053]',  # 保持不变
        '[0054]': '[0054]',  # 保持不变
        '[0055]': '[0055]',  # 保持不变
        '[0056]': '[0056]',  # 保持不变
        '[0057]': '[0057]',  # 保持不变
        '[0058]': '[0058]',  # 保持不变
        '[0059]': '[0059]',  # 保持不变
        
        # 具体实施方式部分应该从[0060]开始
        '[0060]': '[0060]',  # 保持不变
        '[0061]': '[0061]',  # 保持不变
        
        # 修正错误的编号
        '[0111]': '[0062]',
        '[0112]': '[0063]',
        '[0113]': '[0064]',
        '[0114]': '[0065]',
        '[0115]': '[0066]',
        '[0116]': '[0067]',
        '[0117]': '[0068]',
        '[0118]': '[0069]',
        '[0119]': '[0070]',
        '[0120]': '[0071]',
        '[0121]': '[0072]',
    }
    
    # 按照映射替换编号
    for old_num, new_num in number_mapping.items():
        content = content.replace(old_num, new_num)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已最终修正段落编号: {file_path}")

if __name__ == "__main__":
    file_path = "专利方向贮备/4-绿电制氨、掺氨燃烧系统数字孪生/相关专利申请书/一种基于数字孪生技术的绿电制氨掺氨燃烧全链路智能协同控制系统.txt"
    fix_final_numbers(file_path)