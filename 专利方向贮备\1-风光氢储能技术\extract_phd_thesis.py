#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门提取博士论文详细内容的脚本
重点提取算法和技术细节
"""

import fitz  # PyMuPDF
from pathlib import Path
import re

def extract_phd_thesis_content():
    """提取博士论文详细内容"""
    
    pdf_path = Path("01-中文论文/风光氢综合能源系统优化配置与协调控制策略研究[博士论文].PDF")
    
    if not pdf_path.exists():
        print(f"文件不存在: {pdf_path}")
        return
    
    print("🔍 开始提取博士论文详细内容...")
    
    doc = fitz.open(pdf_path)
    total_pages = doc.page_count
    print(f"📚 论文总页数: {total_pages}")
    
    # 提取更多页面内容
    text_content = ""
    
    # 重点提取前30页内容（包含摘要、目录、绪论等）
    for page_num in range(min(30, total_pages)):
        try:
            page = doc[page_num]
            page_text = page.get_text()
            
            if page_text and len(page_text.strip()) > 50:  # 过滤掉几乎空白的页面
                text_content += f"\n{'='*60}\n"
                text_content += f"第{page_num + 1}页内容\n"
                text_content += f"{'='*60}\n"
                text_content += page_text
                text_content += "\n"
        except Exception as e:
            print(f"提取第{page_num + 1}页时出错: {e}")
            continue
    
    doc.close()
    
    # 保存详细内容
    output_file = Path("博士论文详细内容_前30页.txt")
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("风光氢综合能源系统优化配置与协调控制策略研究[博士论文]\n")
        f.write("作者: 孔令国\n")
        f.write("华北电力大学, 2017年\n")
        f.write("="*80 + "\n\n")
        f.write(text_content)
    
    print(f"✅ 详细内容已保存到: {output_file}")
    
    # 分析技术关键词
    print("\n🔑 技术关键词分析:")
    keywords = [
        "化学反应优化算法", "粒子群算法", "HOMER", "PSCAD", "EMTDC",
        "直驱永磁同步", "碱式电解", "质子交换膜", "协调控制", "容量优化",
        "弃风弃光", "功率波动", "利润最大化", "多目标优化", "仿真验证"
    ]
    
    found_keywords = []
    for keyword in keywords:
        if keyword in text_content:
            found_keywords.append(keyword)
            print(f"  ✓ {keyword}")
    
    print(f"\n📊 找到 {len(found_keywords)}/{len(keywords)} 个关键技术词")
    
    return text_content, found_keywords

def extract_algorithm_sections():
    """专门提取算法相关章节"""
    
    pdf_path = Path("01-中文论文/风光氢综合能源系统优化配置与协调控制策略研究[博士论文].PDF")
    
    if not pdf_path.exists():
        return ""
    
    doc = fitz.open(pdf_path)
    algorithm_content = ""
    
    # 查找包含算法描述的页面（通常在中间章节）
    algorithm_keywords = [
        "算法", "优化", "化学反应", "粒子群", "遗传算法", 
        "神经网络", "模糊控制", "模型预测", "控制策略"
    ]
    
    print("\n🔍 搜索算法相关内容...")
    
    for page_num in range(20, min(100, doc.page_count)):  # 搜索第20-100页
        try:
            page = doc[page_num]
            page_text = page.get_text()
            
            # 检查是否包含算法关键词
            keyword_count = sum(1 for keyword in algorithm_keywords if keyword in page_text)
            
            if keyword_count >= 2:  # 至少包含2个算法关键词
                algorithm_content += f"\n{'='*50}\n"
                algorithm_content += f"第{page_num + 1}页 (算法相关)\n"
                algorithm_content += f"{'='*50}\n"
                algorithm_content += page_text[:2000]  # 取前2000字符
                algorithm_content += "\n"
                
                print(f"  ✓ 第{page_num + 1}页: 找到{keyword_count}个算法关键词")
        
        except Exception as e:
            continue
    
    doc.close()
    
    if algorithm_content:
        output_file = Path("博士论文_算法章节.txt")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("博士论文 - 算法相关章节摘录\n")
            f.write("="*60 + "\n")
            f.write(algorithm_content)
        
        print(f"✅ 算法章节已保存到: {output_file}")
    
    return algorithm_content

if __name__ == "__main__":
    try:
        # 提取详细内容
        content, keywords = extract_phd_thesis_content()
        
        # 提取算法章节
        algorithm_content = extract_algorithm_sections()
        
        print(f"\n📄 成功提取 {len(content)} 字符的详细内容")
        print(f"📄 成功提取 {len(algorithm_content)} 字符的算法内容")
        
    except Exception as e:
        print(f"❌ 提取过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
