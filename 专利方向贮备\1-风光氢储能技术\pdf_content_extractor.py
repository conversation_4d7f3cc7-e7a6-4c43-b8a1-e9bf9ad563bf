#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF内容提取器
不依赖外部库，使用基础方法提取PDF文件信息和部分内容
"""

import os
import sys
from pathlib import Path
import re
import subprocess

def find_all_pdfs(base_path):
    """查找所有PDF文件"""
    base_path = Path(base_path)
    pdf_files = []
    
    # 递归查找所有PDF文件
    for pdf_file in base_path.rglob("*.pdf"):
        pdf_files.append(pdf_file)
    for pdf_file in base_path.rglob("*.PDF"):
        pdf_files.append(pdf_file)
    
    return pdf_files

def get_file_info(pdf_path):
    """获取PDF文件基本信息"""
    file_info = {
        'path': str(pdf_path),
        'name': pdf_path.name,
        'size_mb': pdf_path.stat().st_size / 1024 / 1024,
        'parent_dir': pdf_path.parent.name
    }
    return file_info

def analyze_filename(filename):
    """从文件名分析PDF内容"""
    analysis = {
        'type': '未知类型',
        'topic': '',
        'keywords': [],
        'language': 'chinese' if any('\u4e00' <= char <= '\u9fff' for char in filename) else 'english'
    }
    
    # 判断文件类型
    if '博士论文' in filename or '博士' in filename:
        analysis['type'] = '博士学位论文'
    elif '硕士论文' in filename or '硕士' in filename:
        analysis['type'] = '硕士学位论文'
    elif 'CN' in filename and any(char.isdigit() for char in filename):
        analysis['type'] = '中国专利'
    elif 'US' in filename or 'EP' in filename:
        analysis['type'] = '国际专利'
    elif 'paper' in filename.lower() or 'journal' in filename.lower():
        analysis['type'] = '期刊论文'
    
    # 提取技术关键词
    keywords = []
    
    # 中文关键词
    cn_keywords = {
        '风光氢': ['风光氢储能', '可再生能源制氢'],
        '储能': ['储能系统', '能量存储'],
        '数字孪生': ['数字孪生技术', '虚拟仿真'],
        'PID': ['PID控制', '控制器优化'],
        '电解': ['电解水', '电解槽'],
        '制氢': ['氢气生产', '制氢工艺'],
        '优化': ['系统优化', '参数优化'],
        '控制': ['控制策略', '自动控制'],
        '模型预测': ['MPC', '预测控制'],
        '功率': ['功率管理', '功率控制'],
        '协调': ['协调控制', '多系统协调']
    }
    
    # 英文关键词
    en_keywords = {
        'hydrogen': ['hydrogen production', 'hydrogen storage'],
        'wind': ['wind power', 'wind energy'],
        'solar': ['solar energy', 'photovoltaic'],
        'storage': ['energy storage', 'storage system'],
        'control': ['control system', 'control strategy'],
        'optimization': ['system optimization', 'parameter optimization'],
        'digital': ['digital twin', 'digital modeling'],
        'PID': ['PID control', 'PID optimization']
    }
    
    # 检查关键词
    filename_lower = filename.lower()
    for key, values in cn_keywords.items():
        if key in filename:
            keywords.extend(values)
    
    for key, values in en_keywords.items():
        if key in filename_lower:
            keywords.extend(values)
    
    analysis['keywords'] = list(set(keywords))
    
    # 提取主题
    topic = filename.replace('.pdf', '').replace('.PDF', '')
    topic = re.sub(r'\[.*?\]', '', topic)  # 去掉方括号内容
    analysis['topic'] = topic.strip()
    
    return analysis

def try_extract_text_with_cmd(pdf_path):
    """尝试使用系统命令提取文本"""
    try:
        # 尝试使用pdftotext命令（如果系统有安装）
        result = subprocess.run(['pdftotext', str(pdf_path), '-'], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            return result.stdout
    except:
        pass
    
    return None

def extract_pdf_content_basic(pdf_path):
    """基础PDF内容提取（读取原始字节并尝试找到文本）"""
    try:
        with open(pdf_path, 'rb') as file:
            content = file.read()
            
        # 查找PDF中的文本内容
        text_content = ""
        
        # 简单的文本提取：查找PDF中的可打印字符序列
        try:
            # 将字节内容转换为字符串，忽略错误
            raw_text = content.decode('utf-8', errors='ignore')
            
            # 查找连续的可打印字符
            text_patterns = re.findall(r'[a-zA-Z\u4e00-\u9fff\s]{10,}', raw_text)
            text_content = ' '.join(text_patterns[:50])  # 取前50个匹配
            
        except:
            # 如果UTF-8解码失败，尝试其他编码
            try:
                raw_text = content.decode('latin1', errors='ignore')
                text_patterns = re.findall(r'[a-zA-Z\u4e00-\u9fff\s]{10,}', raw_text)
                text_content = ' '.join(text_patterns[:50])
            except:
                text_content = "无法提取文本内容"
        
        # 如果基础方法失败，尝试系统命令
        if len(text_content) < 100:
            cmd_text = try_extract_text_with_cmd(pdf_path)
            if cmd_text:
                text_content = cmd_text[:2000]  # 取前2000字符
        
        return text_content
        
    except Exception as e:
        return f"读取失败: {str(e)}"

def generate_summary_report(pdf_infos, output_path):
    """生成PDF内容总结报告"""
    report_path = output_path / "PDF内容提取报告.txt"
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("PDF文件内容提取报告\n")
        f.write("=" * 60 + "\n\n")
        
        f.write(f"提取时间: {os.popen('echo %date% %time%').read().strip()}\n")
        f.write(f"PDF文件总数: {len(pdf_infos)}\n")
        f.write(f"总文件大小: {sum(info['file_info']['size_mb'] for info in pdf_infos):.2f} MB\n\n")
        
        # 按目录分类统计
        dir_stats = {}
        for info in pdf_infos:
            dir_name = info['file_info']['parent_dir']
            if dir_name not in dir_stats:
                dir_stats[dir_name] = []
            dir_stats[dir_name].append(info)
        
        f.write("📁 目录分布:\n")
        f.write("-" * 30 + "\n")
        for dir_name, files in dir_stats.items():
            f.write(f"{dir_name}: {len(files)} 个文件\n")
        f.write("\n")
        
        # 详细文件信息
        f.write("📋 详细文件信息:\n")
        f.write("-" * 30 + "\n")
        
        for i, info in enumerate(pdf_infos, 1):
            f.write(f"\n{i}. {info['file_info']['name']}\n")
            f.write(f"   路径: {info['file_info']['parent_dir']}/\n")
            f.write(f"   大小: {info['file_info']['size_mb']:.2f} MB\n")
            f.write(f"   类型: {info['analysis']['type']}\n")
            f.write(f"   语言: {info['analysis']['language']}\n")
            f.write(f"   主题: {info['analysis']['topic']}\n")
            
            if info['analysis']['keywords']:
                f.write(f"   关键词: {', '.join(info['analysis']['keywords'][:5])}\n")
            
            # 添加内容摘要
            if 'content_preview' in info and info['content_preview']:
                preview = info['content_preview'][:200] + "..." if len(info['content_preview']) > 200 else info['content_preview']
                f.write(f"   内容预览: {preview}\n")
        
        # 技术关键词统计
        all_keywords = []
        for info in pdf_infos:
            all_keywords.extend(info['analysis']['keywords'])
        
        if all_keywords:
            f.write("\n🔑 技术关键词统计:\n")
            f.write("-" * 30 + "\n")
            keyword_count = {}
            for keyword in all_keywords:
                keyword_count[keyword] = keyword_count.get(keyword, 0) + 1
            
            for keyword, count in sorted(keyword_count.items(), key=lambda x: x[1], reverse=True):
                f.write(f"{keyword}: {count} 次\n")
    
    return report_path

def main():
    """主函数"""
    print("🔍 PDF文件内容提取器")
    print("=" * 50)
    
    # 获取当前目录的父目录（整个风光氢储能技术目录）
    current_dir = Path(__file__).parent
    base_path = current_dir if current_dir.name == "1-风光氢储能技术" else current_dir.parent
    
    print(f"搜索路径: {base_path}")
    
    # 查找所有PDF文件
    pdf_files = find_all_pdfs(base_path)
    
    if not pdf_files:
        print("❌ 未找到PDF文件")
        return
    
    print(f"📚 找到 {len(pdf_files)} 个PDF文件")
    print()
    
    pdf_infos = []
    
    for i, pdf_path in enumerate(pdf_files, 1):
        print(f"📖 处理文件 {i}/{len(pdf_files)}: {pdf_path.name}")
        
        # 获取文件基本信息
        file_info = get_file_info(pdf_path)
        
        # 分析文件名
        analysis = analyze_filename(pdf_path.name)
        
        # 尝试提取内容
        print(f"   正在提取内容...")
        content_preview = extract_pdf_content_basic(pdf_path)
        
        pdf_info = {
            'file_info': file_info,
            'analysis': analysis,
            'content_preview': content_preview
        }
        
        pdf_infos.append(pdf_info)
        
        # 显示基本信息
        print(f"   类型: {analysis['type']}")
        print(f"   大小: {file_info['size_mb']:.2f} MB")
        if analysis['keywords']:
            print(f"   关键词: {', '.join(analysis['keywords'][:3])}")
        print(f"   内容长度: {len(content_preview)} 字符")
        print()
    
    # 生成总结报告
    print("📝 生成总结报告...")
    report_path = generate_summary_report(pdf_infos, base_path)
    print(f"✅ 报告已保存: {report_path.name}")
    
    # 输出总结
    print("\n📊 提取总结:")
    print(f"   PDF文件总数: {len(pdf_infos)}")
    print(f"   总文件大小: {sum(info['file_info']['size_mb'] for info in pdf_infos):.2f} MB")
    
    # 按类型统计
    type_count = {}
    for info in pdf_infos:
        doc_type = info['analysis']['type']
        type_count[doc_type] = type_count.get(doc_type, 0) + 1
    
    print("   文件类型分布:")
    for doc_type, count in type_count.items():
        print(f"     {doc_type}: {count} 个")
    
    return pdf_infos

if __name__ == "__main__":
    try:
        results = main()
        print("\n🎉 PDF内容提取完成！")
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 提取过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
