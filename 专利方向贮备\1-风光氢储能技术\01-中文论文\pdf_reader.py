#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文档内容读取工具
用于提取风光氢储能技术相关论文的文本内容

使用方法:
1. 安装依赖: pip install PyPDF2 pdfplumber
2. 运行脚本: python pdf_reader.py
"""

import os
import sys
import PyPDF2
import pdfplumber
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PDFReader:
    """PDF文档读取器"""
    
    def __init__(self, pdf_path):
        self.pdf_path = Path(pdf_path)
        self.text_content = ""
        self.metadata = {}
        
    def extract_with_pypdf2(self):
        """使用PyPDF2提取文本"""
        try:
            with open(self.pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                # 获取元数据
                if pdf_reader.metadata:
                    self.metadata = {
                        'title': pdf_reader.metadata.get('/Title', ''),
                        'author': pdf_reader.metadata.get('/Author', ''),
                        'subject': pdf_reader.metadata.get('/Subject', ''),
                        'creator': pdf_reader.metadata.get('/Creator', ''),
                        'pages': len(pdf_reader.pages)
                    }
                
                # 提取文本
                text = ""
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            text += f"\n--- 第 {page_num + 1} 页 ---\n"
                            text += page_text + "\n"
                    except Exception as e:
                        logger.warning(f"第 {page_num + 1} 页提取失败: {e}")
                
                self.text_content = text
                return True
                
        except Exception as e:
            logger.error(f"PyPDF2提取失败: {e}")
            return False
    
    def extract_with_pdfplumber(self):
        """使用pdfplumber提取文本（更适合中文）"""
        try:
            with pdfplumber.open(self.pdf_path) as pdf:
                # 获取基本信息
                self.metadata = {
                    'pages': len(pdf.pages),
                    'file_size': os.path.getsize(self.pdf_path)
                }
                
                # 提取文本
                text = ""
                for page_num, page in enumerate(pdf.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            text += f"\n--- 第 {page_num + 1} 页 ---\n"
                            text += page_text + "\n"
                    except Exception as e:
                        logger.warning(f"第 {page_num + 1} 页提取失败: {e}")
                
                self.text_content = text
                return True
                
        except Exception as e:
            logger.error(f"pdfplumber提取失败: {e}")
            return False
    
    def save_text(self, output_path=None):
        """保存提取的文本到文件"""
        if not output_path:
            output_path = self.pdf_path.with_suffix('.txt')
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                # 写入元数据
                f.write("=" * 50 + "\n")
                f.write(f"PDF文档: {self.pdf_path.name}\n")
                f.write("=" * 50 + "\n\n")
                
                if self.metadata:
                    f.write("文档信息:\n")
                    for key, value in self.metadata.items():
                        f.write(f"  {key}: {value}\n")
                    f.write("\n")
                
                # 写入文本内容
                f.write("文档内容:\n")
                f.write("-" * 30 + "\n")
                f.write(self.text_content)
            
            logger.info(f"文本已保存到: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"保存文本失败: {e}")
            return None
    
    def extract_keywords(self, keywords_list):
        """提取包含特定关键词的段落"""
        if not self.text_content:
            return {}
        
        results = {}
        lines = self.text_content.split('\n')
        
        for keyword in keywords_list:
            matching_lines = []
            for i, line in enumerate(lines):
                if keyword in line:
                    # 获取上下文（前后各2行）
                    start = max(0, i-2)
                    end = min(len(lines), i+3)
                    context = '\n'.join(lines[start:end])
                    matching_lines.append({
                        'line_number': i+1,
                        'content': line.strip(),
                        'context': context
                    })
            
            if matching_lines:
                results[keyword] = matching_lines
        
        return results

def analyze_papers():
    """分析当前目录下的PDF论文"""
    current_dir = Path(__file__).parent
    pdf_files = list(current_dir.glob("*.PDF")) + list(current_dir.glob("*.pdf"))
    
    if not pdf_files:
        logger.warning("当前目录下没有找到PDF文件")
        return
    
    # 定义关键词列表
    keywords = [
        "风光氢", "储能", "优化配置", "协调控制", "模型预测控制",
        "功率调控", "能源管理", "系统集成", "控制策略", "算法",
        "仿真", "实验", "结论", "创新点", "技术方案"
    ]
    
    for pdf_file in pdf_files:
        logger.info(f"正在处理: {pdf_file.name}")
        
        # 创建PDF读取器
        reader = PDFReader(pdf_file)
        
        # 尝试提取文本（优先使用pdfplumber）
        success = reader.extract_with_pdfplumber()
        if not success:
            logger.info("尝试使用PyPDF2...")
            success = reader.extract_with_pypdf2()
        
        if success:
            # 保存完整文本
            text_file = reader.save_text()
            
            # 提取关键词相关内容
            keyword_results = reader.extract_keywords(keywords)
            
            if keyword_results:
                # 保存关键词分析结果
                analysis_file = pdf_file.with_name(f"{pdf_file.stem}_关键词分析.txt")
                with open(analysis_file, 'w', encoding='utf-8') as f:
                    f.write(f"《{pdf_file.name}》关键词分析报告\n")
                    f.write("=" * 60 + "\n\n")
                    
                    for keyword, matches in keyword_results.items():
                        f.write(f"关键词: {keyword}\n")
                        f.write(f"出现次数: {len(matches)}\n")
                        f.write("-" * 30 + "\n")
                        
                        for match in matches[:5]:  # 只显示前5个匹配
                            f.write(f"行号: {match['line_number']}\n")
                            f.write(f"内容: {match['content']}\n")
                            f.write(f"上下文:\n{match['context']}\n")
                            f.write("-" * 20 + "\n")
                        f.write("\n")
                
                logger.info(f"关键词分析已保存到: {analysis_file}")
        else:
            logger.error(f"无法提取 {pdf_file.name} 的内容")

def extract_summary(pdf_path):
    """提取论文摘要和结论部分"""
    reader = PDFReader(pdf_path)
    
    if reader.extract_with_pdfplumber():
        text = reader.text_content
        
        # 查找摘要
        abstract_keywords = ["摘要", "摘　要", "ABSTRACT", "Abstract"]
        summary_text = ""
        
        for keyword in abstract_keywords:
            if keyword in text:
                # 简单的摘要提取逻辑
                start_idx = text.find(keyword)
                if start_idx != -1:
                    # 查找摘要结束位置（通常是关键词或下一个章节）
                    end_keywords = ["关键词", "关键字", "KEY WORDS", "Keywords", "第一章", "1.", "引言"]
                    end_idx = len(text)
                    
                    for end_keyword in end_keywords:
                        temp_idx = text.find(end_keyword, start_idx + len(keyword))
                        if temp_idx != -1 and temp_idx < end_idx:
                            end_idx = temp_idx
                    
                    summary_text = text[start_idx:end_idx].strip()
                    break
        
        return summary_text
    
    return None

if __name__ == "__main__":
    print("PDF文档内容读取工具")
    print("=" * 40)
    
    # 检查依赖库
    try:
        import PyPDF2
        import pdfplumber
        print("✓ 依赖库检查通过")
    except ImportError as e:
        print(f"✗ 缺少依赖库: {e}")
        print("请运行: pip install PyPDF2 pdfplumber")
        sys.exit(1)
    
    # 分析PDF文档
    analyze_papers()
    
    print("\n处理完成！")
    print("生成的文件:")
    print("- *.txt: 完整文本内容")
    print("- *_关键词分析.txt: 关键词分析报告")