#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速PDF论文分析工具
专门用于提取学位论文的关键信息

使用方法:
1. 确保安装了依赖: pip install pdfplumber
2. 运行: python quick_pdf_analyzer.py
"""

import os
import re
from pathlib import Path

def simple_pdf_read():
    """简单的PDF文本提取（不依赖外部库）"""
    current_dir = Path(__file__).parent
    pdf_files = list(current_dir.glob("*.PDF")) + list(current_dir.glob("*.pdf"))
    
    print("发现的PDF文件:")
    for i, pdf_file in enumerate(pdf_files, 1):
        print(f"{i}. {pdf_file.name}")
        print(f"   文件大小: {pdf_file.stat().st_size / 1024 / 1024:.2f} MB")
    
    return pdf_files

def analyze_filename(filename):
    """从文件名分析论文信息"""
    analysis = {
        'type': '未知',
        'topic': '',
        'keywords': []
    }
    
    # 判断论文类型
    if '博士论文' in filename or '博士' in filename:
        analysis['type'] = '博士学位论文'
    elif '硕士论文' in filename or '硕士' in filename:
        analysis['type'] = '硕士学位论文'
    elif '期刊' in filename:
        analysis['type'] = '期刊论文'
    
    # 提取关键词
    keywords = []
    if '风光氢' in filename:
        keywords.append('风光氢系统')
    if '储能' in filename:
        keywords.append('储能技术')
    if '优化' in filename:
        keywords.append('系统优化')
    if '控制' in filename:
        keywords.append('控制策略')
    if '模型预测' in filename:
        keywords.append('模型预测控制')
    if '功率' in filename:
        keywords.append('功率调控')
    
    analysis['keywords'] = keywords
    
    # 提取主题
    # 去掉文件扩展名和论文类型标识
    topic = filename.replace('.PDF', '').replace('.pdf', '')
    topic = re.sub(r'\[.*?\]', '', topic)  # 去掉方括号内容
    analysis['topic'] = topic.strip()
    
    return analysis

def try_extract_with_pdfplumber():
    """尝试使用pdfplumber提取内容"""
    try:
        import pdfplumber
        
        current_dir = Path(__file__).parent
        pdf_files = list(current_dir.glob("*.PDF")) + list(current_dir.glob("*.pdf"))
        
        for pdf_file in pdf_files:
            print(f"\n正在分析: {pdf_file.name}")
            print("=" * 50)
            
            try:
                with pdfplumber.open(pdf_file) as pdf:
                    total_pages = len(pdf.pages)
                    print(f"总页数: {total_pages}")
                    
                    # 提取前几页内容（通常包含摘要）
                    first_pages_text = ""
                    for i in range(min(5, total_pages)):
                        page_text = pdf.pages[i].extract_text()
                        if page_text:
                            first_pages_text += page_text + "\n"
                    
                    # 查找摘要
                    abstract = extract_abstract(first_pages_text)
                    if abstract:
                        print("摘要内容:")
                        print("-" * 30)
                        print(abstract[:500] + "..." if len(abstract) > 500 else abstract)
                    
                    # 查找关键词
                    keywords = extract_keywords_from_text(first_pages_text)
                    if keywords:
                        print(f"\n关键词: {', '.join(keywords)}")
                    
                    # 保存分析结果
                    save_analysis_result(pdf_file, {
                        'pages': total_pages,
                        'abstract': abstract,
                        'keywords': keywords,
                        'first_pages': first_pages_text[:1000]
                    })
                    
            except Exception as e:
                print(f"处理 {pdf_file.name} 时出错: {e}")
                
    except ImportError:
        print("pdfplumber库未安装，请运行: pip install pdfplumber")
        return False
    
    return True

def extract_abstract(text):
    """从文本中提取摘要"""
    abstract_patterns = [
        r'摘\s*要[：:]\s*(.*?)(?=关键词|关键字|第一章|1\.|引言)',
        r'摘\s*要\s*(.*?)(?=关键词|关键字|第一章|1\.|引言)',
        r'ABSTRACT[：:]\s*(.*?)(?=KEY\s*WORDS|Keywords|关键词)',
    ]
    
    for pattern in abstract_patterns:
        match = re.search(pattern, text, re.DOTALL | re.IGNORECASE)
        if match:
            abstract = match.group(1).strip()
            # 清理文本
            abstract = re.sub(r'\s+', ' ', abstract)
            return abstract
    
    return None

def extract_keywords_from_text(text):
    """从文本中提取关键词"""
    keyword_patterns = [
        r'关键词[：:]\s*(.*?)(?=\n\n|\n[A-Z]|第一章)',
        r'关键字[：:]\s*(.*?)(?=\n\n|\n[A-Z]|第一章)',
        r'KEY\s*WORDS[：:]\s*(.*?)(?=\n\n|\n[一二三四五六七八九十])',
        r'Keywords[：:]\s*(.*?)(?=\n\n|\n[一二三四五六七八九十])',
    ]
    
    for pattern in keyword_patterns:
        match = re.search(pattern, text, re.DOTALL | re.IGNORECASE)
        if match:
            keywords_text = match.group(1).strip()
            # 分割关键词
            keywords = re.split(r'[;；,，\s]+', keywords_text)
            keywords = [kw.strip() for kw in keywords if kw.strip()]
            return keywords[:10]  # 最多返回10个关键词
    
    return []

def save_analysis_result(pdf_file, analysis):
    """保存分析结果"""
    output_file = pdf_file.with_name(f"{pdf_file.stem}_分析结果.txt")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"论文分析报告\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"文件名: {pdf_file.name}\n")
        f.write(f"页数: {analysis.get('pages', '未知')}\n\n")
        
        if analysis.get('abstract'):
            f.write("摘要:\n")
            f.write("-" * 20 + "\n")
            f.write(analysis['abstract'] + "\n\n")
        
        if analysis.get('keywords'):
            f.write("关键词:\n")
            f.write("-" * 20 + "\n")
            f.write(", ".join(analysis['keywords']) + "\n\n")
        
        if analysis.get('first_pages'):
            f.write("前几页内容预览:\n")
            f.write("-" * 20 + "\n")
            f.write(analysis['first_pages'] + "\n")
    
    print(f"分析结果已保存到: {output_file.name}")

def main():
    """主函数"""
    print("PDF论文快速分析工具")
    print("=" * 40)
    
    # 1. 基于文件名的分析
    pdf_files = simple_pdf_read()
    
    if not pdf_files:
        print("当前目录下没有找到PDF文件")
        return
    
    print("\n基于文件名的分析:")
    print("-" * 30)
    
    for pdf_file in pdf_files:
        analysis = analyze_filename(pdf_file.name)
        print(f"\n文件: {pdf_file.name}")
        print(f"类型: {analysis['type']}")
        print(f"主题: {analysis['topic']}")
        if analysis['keywords']:
            print(f"关键词: {', '.join(analysis['keywords'])}")
    
    # 2. 尝试提取PDF内容
    print(f"\n尝试提取PDF内容...")
    print("-" * 30)
    
    success = try_extract_with_pdfplumber()
    
    if not success:
        print("\n建议:")
        print("1. 运行 install_requirements.bat 安装依赖库")
        print("2. 或手动运行: pip install pdfplumber")
        print("3. 然后重新运行此脚本")

if __name__ == "__main__":
    main()