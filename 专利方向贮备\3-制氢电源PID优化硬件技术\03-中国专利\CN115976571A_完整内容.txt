CN 115976571 A
(19)国家知识产权局
P
(21)申请号 202211585783.5
(22)申请日 2022.12.09
(12)发明专利申请
(71)申请人 北京奇稳新能源科技有限公司
地址 100072 北京市丰台区长辛店杨公庄1
号2-4幢5层505号
(72)发明人于红付强
(74) 专利代理机构 北京品源专利代理有限公司
11332
专利代理师 李礼
(51) Int.Cl.
C25B 15/02 (2021.01)
C25B 1/04 (2021.01)
C25B 9/65 (2021.01)
(10)申请公布号 CN 115976571 A
(43)申请公布日 2023.04.18
(54)发明名称
一种具有需求侧响应的制氢控制系统
(57) 摘要
本发明公开了一种具有需求侧响应的制氢
控制系统,用于控制制氢装置进行制氢,包括:不
控整流单元、DC/DC电压转换单元和制氢电源控
制器;不控整流单元用于将交流电网的交流信号
转换为直流信号;制氢电源控制器与DC/DC电压
转换单元的控制端电连接;制氢电源控制器用于
控制DC/DC电压转换单元将直流信号转换为具有
恒定电压的供电电源,以控制制氢装置启动;制
氢电源控制器还用于在制氢装置启动后,实时获
取制氢装置的温度,且在制氢装置的溫度大于或
等于预设温度时,控制DC/DC电压转换单元将直
流信号转换为具有恒定电流的供电电源,以控制
制氢装置进行制氢。本发明的技术方案可以提高
制氢的质量和稳定性。
交流电网
权利要求书3页 说明书9页附图2页
30
制氢电源
控制器
10
40
41
公共直流
DC/DC
144
本
母线
20
电压转
换
单元
制氢装
置
CN 115976571 A
权利要求书
1/3页
1.一种具有需求侧响应的制氢控制系统,用于控制制氢装置进行制氢,其特征在于,包
括:不控整流单元、DC/DC电压转换单元和制氢电源控制器;
所述不控整流单元的交流端与交流电网电连接,所述不控整流单元的直流端与通过公
共直流母线与所述DC/DC电压转换的输入端电连接;所述DC/DC电压转换单元的输出端与所
述制氢装置的电源端电连接;
所述不控整流单元用于将所述交流电网的交流信号转换为直流信号;
所述制氢电源控制器与所述DC/DC电压转换单元的控制端电连接;所述制氢电源控制
器用于控制所述DC/DC电压转换单元将所述直流信号转换为具有恒定电压的供电电源,以
控制所述制氢装置启动;
所述制氢电源控制器还用于在所述制氢装置启动后,实时获取所述制氢装置的溫度,
且在所述制氢装置的溫度大于或等于预设温度时,控制所述DC/DC电压转换单元将所述直
流信号转换为具有恒定电流的供电电源,以控制所述制氢装置进行制氢。
2.根据权利要求1所述的具有需求侧响应的制氢控制系统,其特征在于,
所述制氢电源控制器具体用于:
获取所述制氢装置的预设启动电压、预设参考电流,以及实时获取所述DC/DC电压转换
单元的当前输出电压和所述DC/DC电压转换单元的当前输出电流;
在所述制氢装置启动时,根据所述预设启动电压、所述DC/DC电压转换单元的当前输出
电压和所述DC/DC电压转换单元的当前输出电流,基于电压外环、电流内环的第一PI控制算
法,确定向所述DC/DC电压转换单元提供的启动控制信号;
在所述制氢装置启动后,实时获取所述制氢装置的为当前温度;
在所述制氢装置的当前溫度大于或等于所述预设温度时,根据所述预设参考电流、所
述DC/DC电压转换单元的当前输出电流,基于第二PI控制算法,确定向所述DC/DC电压转换
单元提供的运行控制信号;
所述DC/DC电压转换单元具体用于:
根据所述启动控制信号,将所述直流信号转换为具有恒定电压的供电电源,并提供至
所述制氢装置;
根据所述运行控制信号,将所述直流信号转换为具有恒定电流的供电电源,并提供至
所述制氢装置。
3.根据权利要求2所述的具有需求侧响应的制氢控制系统,其特征在于,所述第一PI控
制算法的计算公式包括电压外环计算公式和电流内环计算公式;
所述电压外环计算公式为:
Iout ut = Kp1(Uset - Uout) + K11 (Uset - Uout)
其中,Iout为电压外环输出信号,Uset为所述预设启动电压,Uout为所述DC/DC电压转换单
元的当前输出电压,kp₁和k₁₁分别为所述第一PI控制算法的电压外环比例常数和积分常数;
所述电流内环计算公式为:
u1 = Kp2 (lout - Iout) + K12 (I out - Iout)
P2
-
2
CN 115976571 A
权利要求书
2/3页
其中,ul为所述启动控制信号,Iout为所述DC/DC电压转换单元的当前输出电流,kp2和k12
分别为所述第一PI控制算法的电流内环比例常数和积分常数。
4.根据权利要求2所述的具有需求侧响应的制氢控制系统,其特征在于,所述第二PI控
制算法的计算公式为:
u2=kp3 (Iref-Iout) +K13S (Iref-Iout)
其中,u2为所述运行控制信号,Iref为所述预设参考电流,Iout为所述DC/DC电压转换单
元的当前输出电流,kp3和k13分别为所述第二PI控制算法的比例常数和积分常数。
5.权利要求2所述的具有需求侧响应的制氢控制系统,其特征在于,所述预设参考电流
基于所述制氢装置的额定功率和额定电流、所述制氢装置中电解槽的伏安特性曲线以及所
述不控整流单元的交流端或直流端输入的有功功率进行确定。
6.根据权利要求1所述的具有需求侧响应的制氢控制系统,其特征在于,还包括:制氢
辅助装置和制氢辅助控制器;
所述制氢辅助装置包括水泵和冷却机构;
所述制氢辅助控制器用于:
获取所述制氢装置中氢气温度和氧气温度;在所述氢气温度和所述氧气温度之和高于
温度上限时,控制所述冷却机构启动,对所述制氢装置进行降温,以及在所述氢气温度和所
述氧气温度之和低于温度下限时,控制所述冷却机构关闭,停止对所述制氢装置进行降温;
和/或,
获取所述制氢装置的分离器中的压力值和液位值;根据所述压力值和所述液位值,基
于PID控制算法,控制所述水泵的工作状态。
7.根据权利要求1所述的具有需求侧响应的制氢控制系统,其特征在于,还包括:主控
制器和飞轮储能装置;
所述飞轮储能装置分别与所述公共直流母线和所述主控制器连接;
所述主控制器用于获取储能调节信号,并根据所述储能调节信号,控制所述飞轮储能
装置向所述公共直流母线提供有功功率或无功功率,或者,控制所述飞轮储能装置从所述
公共直流母线吸收有功功率或无功功率。
8.根据权利要求7所述的具有需求侧响应的制氢控制系统,其特征在于,所述储能调节
信号包括所述公共直流母线电压变化量和/或所述不控整流单元输入端的波动功率;
所述主控制器具体用于:根据所述公共直流母线电压变化量和/或所述不控整流单元
输入端的波动功率,控制所述飞轮储能装置从所述公共直流母线吸收有功功率或无功功
率,或者,控制所述飞轮储能装置向所述公共直流母线输出有功功率或无功功率。
9.根据权利要求7所述的具有需求侧响应的制氢控制系统,其特征在于,所述主控制器
还用于获取上级调度指令,并根据所述上级调度指令,调节所述飞轮储能装置的输入/输出
功率。
10.根据权利要求7所述的具有需求侧响应的制氢控制系统,其特征在于,所述主控制
器还用于在所述飞轮装置投入运行前,获取飞轮储能装置的储能能量,根据所述储能能量,
控制所述飞轮储能装置从所述公共直流母线吸收能量,以使所述飞轮储能装置的储能能量
达到预设储能能量。
11.根据权利要求7所述的具有需求侧响应的制氢控制系统,其特征在于,所述飞轮储
3
CN 115976571 A
权利要求书
能装置的储能模块包括超级电容器。
3/3页
12.根据权利要求1所述的具有需求侧响应的制氢控制系统,其特征在于,还包括:滤波
单元;
所述滤波单元与所述交流电网电连接;所述滤波单元用于对所述交流电网的交流信号
进行滤波。
13.根据权利要求1所述的具有需求侧响应的制氢控制系统,其特征在于,所述不控整
流单元包括12脉不控整流器、24脉不控整流器和48脉不控整流器中的一种。
14.根据权利要求1所述的具有需求侧响应的制氢控制系统,其特征在于,还包括:整流
变压器;
所述整流变压器电连接于所述交流电网与所述不控整流单元之间。
15.根据权利要求14所述的具有需求侧响应的制氢控制系统,其特征在于,所述整流变
压器包括12脉整流变压器、24脉整流变压器和48脉整流变压器中的一种。
4
CN 115976571 A
说明书
1/9页
一种具有需求侧响应的制氢控制系统
技术领域
[0001] 本发明涉及氢能利用技术,特别是一种具有需求侧响应的制氢控制系统。
背景技术
[0002] 为应对气候变化、推动绿色发展,我国大力发展新能源,氢能利用技术作为一种零
污染的性能源技术,成为当前新能源技术的热门研究方向。
[0003] 氢能利用技术的关键在于氢气的制备和存储,现有技术中采用制氢装置进行制
氢,该制氢装置包括电解槽,制氢电源对电解槽中的电解液进行电解,产生氢气,达到制氢
的目的。
[0004] 但是,现有技术中,为制氢装置的电解槽所提供的制氢电源通常为具有固定电压
的电源信号,而在制氢过程中容易受到外界因素等影响,导致制氢的电流会发生波动,影响
电解过程,进而影响制氢的质量和稳定性。
发明内容
[0005] 本发明实施例提供了一种具有需求侧响应的制氢控制系统,可以高质量且稳定地
制氢。
[0006] 第一方面,本发明实施例提供了一种具有需求侧响应的制氢控制系统,用于控制
制氢装置进行制氢,包括:不控整流单元、DC/DC电压转换单元和制氢电源控制器;
[0007] 所述不控整流单元的交流端与交流电网电连接,所述不控整流单元的直流端与通
过公共直流母线与所述DC/DC电压转换的输入端电连接;所述DC/DC电压转换单元的输出端
与所述制氢装置的电源端电连接;
[0008]
[0009]
所述不控整流单元用于将所述交流电网的交流信号转换为直流信号;
所述制氢电源控制器与所述DC/DC电压转换单元的控制端电连接;所述制氢电源
控制器用于控制所述DC/DC电压转换单元将所述直流信号转换为具有恒定电压的供电电
源,以控制所述制氢装置启动;
[0010]
所述制氢电源控制器还用于在所述制氢装置启动后,实时获取所述制氢装置的温
度,且在所述制氢装置的温度大于或等于预设温度时,控制所述DC/DC电压转换单元将所述
直流信号转换为具有恒定电流的供电电源,以控制所述制氢装置进行制氢。
[0011]
[0012]
可选的,所述制氢电源控制器具体用于:
获取所述制氢装置的预设启动电压、预设参考电流,以及实时获取所述DC/DC电压
转换单元的当前输出电压和所述DC/DC电压转换单元的当前输出电流;
[0013] 在所述制氢装置启动时,根据所述预设启动电压、所述DC/DC电压转换单元的当前
输出电压和所述DC/DC电压转换单元的当前输出电流,基于电压外环、电流内环的第一PI控
制算法,确定向所述DC/DC电压转换单元提供的启动控制信号;
[0014]
在所述制氢装置启动后,实时获取所述制氢装置的为当前温度;
[0015] 在所述制氢装置的当前温度大于或等于所述预设温度时,根据所述预设参考电
5
CN 115976571 A
说明书
2/9页
流、所述DC/DC电压转换单元的当前输出电流,基于第二PI控制算法,确定向所述DC/DC电压
转换单元提供的运行控制信号;
[0016]
所述DC/DC电压转换单元具体用于:
[0017] 根据所述启动控制信号,将所述直流信号转换为具有恒定电压的供电电源,并提
供至所述制氢装置;
[0018] 根据所述运行控制信号,将所述直流信号转换为具有恒定电流的供电电源,并提
供至所述制氢装置。
[0019] 可选的,所述第一PI控制算法的计算公式包括电压外环计算公式和电流内环计算
公式;
[0020] 所述电压外环计算公式为:
f
[0021] Iout = Kp1 (Uset - Uout) + K11 (Uset - Uout)
[0022] 其中,Iout为电压外环输出信号,U为所述预设启动电压,U为所述DC/DC电压
set
转换单元的当前输出电压,kp₁和k₁₁分别为所述第一PI控制算法的电压外环比例常数和积
分常数;
[0023] 所述电流内环计算公式为:
*
-
[0024] u1 = Kp2(Gout - Jout) + K12 f (lout - Iout)
[0025] 其中,ul为所述启动控制信号,Iout为所述DC/DC电压转换单元的当前输出电流,kp2
和k12分别为所述第一PI控制算法的电流内环比例常数和积分常数。
[0026]
[0027]
[0028]
可选的,所述第二PI控制算法的计算公式为:
u2=kp3 (Iref-Iout) +K13S (Iref-Iout)
其中,u2为所述运行控制信号,IF为所述预设参考电流,I为所述DC/DC电压转
ref
out
换单元的当前输出电流,kp3和k分别为所述第二PI控制算法的比例常数和积分常数。
[0029] 可选的,所述预设参考电流基于所述预设参考电流基于所述制氢装置的额定功率
和额定电流、所述制氢装置中电解槽的伏安特性曲线以及所述不控整流单元的交流端或直
流端输入的有功功率进行确定。
[0030]
器;
可选的,具有需求侧响应的制氢控制系统还包括:制氢辅助装置和制氢辅助控制
[0031] 所述制氢辅助装置包括水泵和冷却机构;
[0032] 所述制氢辅助控制器用于:
[0033]
获取所述制氢装置中氢气温度和氧气温度;在所述氢气温度和所述氧气温度之和
高于温度上限时,控制所述冷却机构启动,对所述制氢装置进行降溫,以及在所述氢气温度
和所述氧气温度之和低于温度下限时,控制所述冷却机构关闭,停止对所述制氢装置进行
降温;和/或,获取所述制氢装置的分离器中的压力值和液位值;根据所述压力值和所述液
位值,基于PID控制算法,控制所述水泵的工作状态。
[0034]
[0035] 所述飞轮储能装置分别与所述公共直流母线和所述主控制器连接;
可选的,具有需求侧响应的制氢控制系统还包括:主控制器和飞轮储能装置;
6
CN 115976571 A
说明书
3/9页
[0036] 所述主控制器用于获取储能调节信号,并根据所述储能调节信号,控制所述飞轮
储能装置向所述公共直流母线提供有功功率或无功功率,或者,控制所述飞轮储能装置从
所述公共直流母线吸收有功功率或无功功率。
[0037] 可选的,所述储能调节信号包括所述公共直流母线电压变化量和/或所述不控整
流单元输入端的波动功率;
[0038] 所述主控制器具体用于:根据所述公共直流母线电压变化量和/或所述不控整流
单元输入端的波动功率,控制所述飞轮储能装置从所述公共直流母线吸收有功功率或无功
功率,或者,控制所述飞轮储能装置向所述公共直流母线输出有功功率或无功功率。
[0039] 可选的,所述主控制器还用于获取上级调度指令,并根据所述上级调度指令,调节
所述飞轮储能装置的输入/输出功率。
[0040] 可选的,所述主控制器还用于在所述飞轮装置投入运行前,获取飞轮储能装置的
储能能量,根据所述储能能量,控制所述飞轮储能装置从所述公共直流母线吸收能量,以使
所述飞轮储能装置的储能能量达到预设储能能量。
[0041] 可选的,所述飞轮储能装置的储能模块包括超级电容器。
[0042] 可选的,具有需求侧响应的制氢控制系统还包括:滤波单元;
[0043]
所述滤波单元与所述交流电网电连接;所述滤波单元用于对所述交流电网的交流
信号进行滤波。
[0044] 可选的,所述不控整流单元包括12脉不控整流器、24脉不控整流器和48脉不控整
流器中的一种。
[0045] 可选的,具有需求侧响应的制氢控制系统还包括:整流变压器;
[0046] 所述整流变压器电连接于所述交流电网与所述不控整流单元之间。
[0047] 可选的,所述整流变压器包括12脉整流变压器、24脉整流变压器和48脉整流变压
器中的一种。
[0048] 本发明的技术方案,在制氢装置启动时,通过不控整流单元将交流电网的交流信
号转换为直流信号,以满足制氢装置的供电需求,再由制氢电源控制器控制DC/DC电压转换
单元将直流信号转换为具有恒定电压的供电电源,并提供至制氢装置,进而控制制氢装置
稳定且快速地启动;而在制氢装置启动后,且其温度达到预设温度时,制氢电源控制器控制
DC/DC电压转换单元将交流电网的交流信号转换为具有恒定电流的供电电源,以控制制氢
装置进行制氢,从而可以实现高质量且稳定地制氢。
附图说明
[0049]
图1是本发明实施例提供的一种具有需求侧响应的制氢控制系统结构示意图;
[0050] 图2是本发明实施例提供的一种整流变压器和不控整流单元的结构示意图;
[0051] 图3是本发明实施例提供的另一种具有需求侧响应的制氢控制系统结构示意图。
具体实施方式
[0052]
下面结合附图和实施例对本发明作进一步的详细说明。可以理解的是,此处所描
述的具体实施例仅仅用于解释本发明,而非对本发明的限定。另外还需要说明的是,为了便
于描述,附图中仅示出了与本发明相关的部分而非全部结构。
7
CN 115976571 A
说明书
4/9页
[0053] 在更加详细地讨论示例性实施例之前应当提到的是,一些示例性实施例被描述成
作为流程图描绘的处理或方法。虽然流程图将各项操作(或步骤) 描述成顺序的处理,但是
其中的许多操作可以被并行地、并发地或者同时实施。此外,各项操作的顺序可以被重新安
排。当其操作完成时所述处理可以被终止,但是还可以具有未包括在附图中的附加步骤。所
述处理可以对应于方法、函数、规程、子例程、子程序等等。
[0054] 本发明使用的术语“包括”及其变形是开放性包括,即“包括但不限于”。术语“基
于”是“至少部分地基于”。术语“一个实施例”表示“至少一个实施例”。
[0055] 需要注意,本发明中提及的“第一”、“第二”等概念仅用于对相应内容进行区分,并
非用于限定顺序或者相互依存关系。
[0056] 需要注意,本发明中提及的“一个”、“多个”的修饰是示意性而非限制性的,本领域
技术人员应当理解,除非在上下文另有明确指出,否则应该理解为“一个或多个”。
[0057] 本发明实施例提供了一种具有需求侧响应的制氢控制系统,该具有需求侧响应的
制氢控制系统用于控制制氢装置进行制氢,该制氢装置的电解槽可以包括但不限于碱性电
解槽、质子交换膜电解槽或固体氧化物电解槽等,制氢装置所产生的请其可用于化工工业、
燃料电池发电等。图1是本发明实施例提供的一种具有需求侧响应的制氢控制系统结构示
意图,如图1所示,具有需求侧响应的制氢控制系统包括:不控整流单元40、DC/DC电压转换
单元20和制氢电源控制器30;不控整流单元40的交流端与交流电网电连接,不控整流单元
40的直流端通过公共直流母线与DC/DC电压转换单元20的输入端电连接;DC/DC电压转换单
元20的输出端与制氢装置10的电源端电连接;不控整流单元40用于将交流电网的交流信号
转换为直流信号;制氢电源控制器30与DC/DC电压转换单元20的控制端电连接;制氢电源控
制器30用于控制DC/DC电压转换单元20将直流信号转换为具有恒定电压的供电电源,以控
制制氢装置10启动;制氢电源控制器30还用于在制氢装置10启动后,实时获取制氢装置10
的温度,且在制氢装置10的溫度大于或等于预设温度时,控制DC/DC电压转换单元20将直流
信号转换为具有恒定电流的供电电源,以控制制氢装置10进行制氢。
[0058] 其中,预设温度可以理解为制氢装置可以进行正常制氢工作的温度,该温度可以
根据用户需求以及制氢装置工作状态进行设定。
[0059] 具体的,参考图1,由于制氢装置10工作电源为恒定的直流电源,而交流电网提供
的电源信号为波动的交流信号,因此在制氢装置10开始工作时,首先需要不控整流单元40
将交流电网的交流信号转换为直流信号,以满足制氢装置10的供电需求;同时,在制氢装置
10启动时,制氢电源控制器30控制DC/DC电压转换单元20将直流信号转换为具有恒定电压
的供电电源,以控制制氢装置10快速且稳定地启动。同时,在制氢装置10完成启动后,制氢
电源控制器30通过传感器实时获取制氢装置10的温度,当制氢装置10的溫度大于或等于预
设温度时,制氢电源控制器30控制DC/DC电压转换单元20将交流电网的交流信号转换为具
有恒定电流的直流信号,进而控制制氢装置10进行稳定且高质量地制氢。
[0060] 本发明实施例通过在制氢装置启动时,不控整流单元将交流电网的交流信号转换
为直流信号,以满足制氢装置的供电需求,然后制氢电源控制器控制DC/DC电压转换单元将
直流信号转换为具有恒定电压的供电电源,并提供至制氢装置,进而控制制氢装置稳定且
快速地启动;而在制氢装置启动后,且其温度达到预设温度时,制氢电源控制器控制DC/DC
电压转换单元将交流电网的交流信号转换为具有恒定电流的供电电源,以控制制氢装置进
8
CN 115976571 A
说明书
行制氢,从而可以实现高质量且稳定地制氢。
5/9页
[0061] 可选的,继续参考图1,制氢控制系统还包括整流变压器41;整流变压器41电连接
于交流电网与不控整流单元40之间,用于将交流电网的交流信号改变为不控整流单元40所
需要的电源信号。
[0062] 可选的,不控整流单元40包括12脉不控整流器、24脉不控整流器和48脉不控整流
器中的一种。相应的,整流变压器40同样可以包括12脉整流变压器、24脉整流变压器和48脉
整流变压器中的一种。
[0063] 示例性的,图2是本发明实施例提供的一种整流变压器和不控整流单元的结构示
意图,如图2所示,不控整流单元40包括12脉不控整流器,该12脉不控整流器包括两组桥臂:
三个上桥臂21、三个下桥臂22,每一个桥臂包括两个串联连接的晶闸管23。此时,整流变压
器41为12脉整流变压器,该12脉整流变压器的原边接入交流电网,副边两组绕组相差30°,
该两组副变绕组分别连接于上桥臂21和下桥臂22,用于将交流电网的电压改变为不控整流
单元40需要的电压,进而通过不控整流单元40将该电压转化为供电电源,输送给后续制氢
装置10进行制氢。
[0064] 需要注意的是,整流变压器41脉数与不控整流单元40脉数需一一对应,在本发明
其他实施例中,不控整流单元40还可以包括24脉不控整流器,此时,整流变压器41同样为24
脉整流变压器,其副边绕组为4组,相差15°,相应的连接的桥臂也为4组;或者,不控整流单
元40还可以包括48脉不控整流器,此时,整流变压器41同样为48脉整流变压器,其副边绕组
为8组,相差7.5°,相应的连接的桥臂也为8组。其中,相较于12脉的整流变压器和12脉不控
整流器,24/48脉的整流变压器和24/48脉不控整流器具有更小的谐波和更高的功率因数。
在能够实现本发明实施例的核心发明点的前提下,本发明实施例对不控整流单元和整流变
压器的脉数不做限定。
[0065] 在一可选的实施例中,继续参考图1,制氢电源控制器30具体用于:获取制氢装置
10的预设启动电压、预设参考电流,以及实时获取DC/DC电压转换单元20的当前输出电压和
DC/DC电压转换单元20的当前输出电流;在制氢装置10启动时,根据预设启动电压、DC/DC电
压转换单元20的当前输出电压和DC/DC电压转换单元20的当前输出电流,基于电压外环、电
流内环的第一PI控制算法,确定向DC/DC电压转换单元20提供的启动控制信号;在制氢装置
10启动后,实时获取制氢装置10的当前温度;在制氢装置10的当前溫度大于或等于预设温
度时,根据预设参考电流、DC/DC电压转换单元20的当前输出电流,基于第二PI控制算法,确
定向DC/DC电压转换单元20提供的运行控制信号;DC/DC电压转换单元20具体用于:根据启
动控制信号,将直流信号转换为具有恒定电压的供电电源,并提供至制氢装置10;根据运行
控制信号,将直流信号转换为具有恒定电流的供电电源,并提供至制氢装置10。
[0066] 其中,预设启动电压可以理解为根据实际需要预先设置的制氢装置10启动时所需
的电压;预设参考电流可以理解为制氢装置10正常制氢工作时所需的电流,可以由人工设
定,或基于制氢装置10的额定功率和额定电流、制氢装置中电解槽的伏安特性曲线以及不
控整流单元40的交流端或直流端输入的有功功率进行确定。
[0067] 示例性的,当交流电网侧或公共直流母线侧有新能源接入时,新能源可以为风能、
潮汐能、光伏能等,此时可以由制氢电源额定功率PN、制氢电源额定电流IN、不控整流单元
40的交流端或直流端输入的有功功率PW,即新能源出力,确定出转换电流I,再根据电解槽
9
CN 115976571 A
说明书
6/9页
的伏安特性曲线,确定电解槽的电压U,进而得到参考电流Iref。在一示例性实施例中,当新
能源为光伏能时,提供光伏能的光伏组件能够输出直流的有功功率,使得该光伏组件可以
与公共直流母线电连接,此时可基于不控整流单元40的直流端的有功功率PW确定出转换电
流I;当新能源为潮汐能时,提供潮汐能的潮汐转换装置能够输出交流的有功功率,使得该
潮汐转换装置可以与不控整流单元40的交流端电连接,此时可基于不控整流单元40的交流
端的有功功率PW确定出转换电流I。
[0068] 具体的,由于在制氢装置10启动时,需要向制氢装置10提供具有恒定电压的直流
电源,因此可以先设置该具有恒定电压的直流电源的电压为预设电压,即DC/DC电压转换单
元20的输出端应输出具有预设电压的直流信号,此时,可以获取DC/DC电压转换单元20的输
出端的输出电压,通过第一PI控制算法,确定向DC/DC电压转换单元20提供的启动控制信
号,以使DC/DC电压转换单元20的输出端的输出电压与预设电压保持一致。示例性的,第一
PI控制算法的计算公式包括电压外环计算公式和电流内环计算公式;
[0069]
电压外环计算公式为:
f
[0070] Iout = Kp1 (Uset - Uout) + K11 (Uset - Uout
[0071] 其中,Iout为电压外环输出信号,U为预设启动电压,U为DC/DC电压转换单元20
set
out
的当前输出电压,kp₁和k₁₁分别为第一PI控制算法的电压外环比例常数和积分常数,1<kp₁<
1000,0.01<k₁₁<10。在确定电压外环输出信号Iout为时,可将该电压外环输出信号Iout作为
电流内环的电流参考值,通过电流内环计算公式,得到启动控制信号,该电流内环计算公式
为:
*
*
[0072] u1 = Kp2(lout - Lout) + K12 / (lout - Lout)
[0073] 其中,ul为启动控制信号,Iout为DC/DC电压转换单元20的当前输出电流,kp2和k12
分别为第一PI控制算法的电流內环比例常数和积分常数,1<kp2<1000,0.01<k₁₂<10。此时,
DC/DC电压转换单元20根据启动控制信号,不断进行反馈调节,使得DC/DC电压转换单元20
的输出电压能够与预设电压一致,使得DC/DC电压转换单元20的输出具有恒定电压的供电
电源,并提供至制氢装置10,使得制氢装置10以恒定电压启动。
[0074]
在制氢装置10启动后,制氢电源控制器30实时获取制氢装置10的当前温度,并将
制氢装置10的当前温度与预设温度进行实时对比,在制氢装置10的当前温度大于或等于预
设温度时,根据预设参考电流、DC/DC电压转换单元20的当前输出电流,基于第二PI控制算
法,确定向DC/DC电压转换单元20提供的运行控制信号;其中,第二PI控制算法的计算公式
为:
[0075]
+k
u2=kp3 (Iref-Iout) +K13S (Iref Iout)
ref
out
[0076] 其中,u2为运行控制信号,I为预设参考电流,I为DC/DC电压转换单元的当前
输出电流,kp3和k13分别为第二PI控制算法的比例常数和积分常数。在得到运行控制信号
后,DC/DC电压转换单元20根据该运行控制信号,不断进行反馈调节,使得DC/DC电压转换单
元20的输出电流I能够与预设参考电流I一致,使得DC/DC电压转换单元20的输出具有
恒定电流的直流信号,并提供至制氢装置10,使得制氢装置10以恒定电流运行,进而稳定制
out
ref
10
CN 115976571 A
氢。
说明书
7/9页
[0077] 可选的,图3是本发明实施例提供的另一种具有需求侧响应的制氢控制系统结构
示意图,如图3所示,制氢控制系统还包括:制氢辅助装置50和制氢辅助控制器60;制氢辅助
装置50包括水泵和冷却机构(图中未示出);制氢辅助控制器60用于:获取制氢装置10中氢
气温度和氧气温度;在氢气温度和氧气温度之和高于温度上限时,控制冷却机构启动,对制
氢装置10进行降温,以及在氢气温度和氧气温度之和低于温度下限时,控制冷却机构关闭,
停止对制氢装置10进行降溫;和/或,获取制氢装置10的分离器中的压力值和液位值;根据
压力值和液位值,基于PID控制算法,控制水泵的工作状态。
[0078] 具体的,制氢装置10制取氢气的速率受温度、电解液的压力和液位等因素影响,制
氢辅助控制器60实时获取制氢装置10中氢气温度和氧气温度,在氢气温度和氧气温度之和
高于温度上限时,控制冷却机构启动,对制氢装置10进行降溫,以及在氢气温度和氧气温度
之和低于温度下限时,控制冷却机构关闭,停止对制氢装置10进行降溫;此外,制氢辅助控
制器60还可以获取制氢装置10的分离器中的压力值和液位值,当制氢装置10中氢侧液位高
于液位上限时关闭补水泵,当液位低于液位下限时就打开补水泵补水。其中,对分离器中的
压力和液位控制采用的是基于PID控制算法进行反馈调节,其输入值为制氢装置10的分离
器中的压力值和液位值,输出值为水泵的开关调节信号。通过对制氢装置10的溫度以及制
氢装置10的分离器中的压力值和液位值的调节,确保了制氢质量。
[0079] 可选的,参考图3,制氢控制系统还包括:主控制器70和飞轮储能装置80;飞轮储能
装置80分别与公共直流母线和主控制器70连接;主控制器70用于获取储能调节信号,并根
据储能调节信号,控制飞轮储能装置80向公共直流母线提供有功功率或无功功率,或者,控
制飞轮储能装置80从公共直流母线吸收有功功率或无功功率。可选的,主控制器70还可以
与制氢电源控制器30电/通讯连接,用于使主控制器70能根据储能调节信号调整制氢电源
控制器30的控制策略。
[0080] 其中,储能调节信号包括公共直流母线电压变化量和/或不控整流单元40输入端
的波动功率;此时,主控制器70可以根据公共直流母线电压变化量和/或不控整流单元40输
入端的波动功率,控制飞轮储能装置80从公共直流母线吸收有功功率或无功功率,或者,控
制飞轮储能装置80向公共直流母线输出有功功率或无功功率。
[0081]
示例性的,当交流电网的交流信号由风电转换装置提供时,其向制氢系统提供的
电压有可能为较小的电压,同时向不控整流单元40输入端输入的功率向下波动,不控整流
单元40所转换的直流信号提供至公共直流母线后,使得公共直流母线的电压也会向下波
动,该公共直流母线的直流信号经DC/DC电压转换单元20进行转换后,也会发生相应的波
动,相应的,制氢装置10的输入功率不平滑,制氢质量受到影响,主控制器70获取到公共直
流母线电压变化量和/或不控整流单元40输入端的波动功率,即储能调节信号,控制飞轮储
能装置80的电机转矩,使得飞轮转速升高,进而向公共直流母线输出有功功率或无功功率,
使公共直流母线电压以及不控整流单元40输入端的功率均为稳定的值,确保制氢装置10的
制氢质量;当公共直流母线中还接入有光伏组件时,晴朗的日间太阳能充足,此时光伏组件
能够提供较多的电信号至公共直流母线,使得向不控整流单元40输入端的功率向上波动,
公共直流母线的电压也向上波动,相应的制氢装置10的输入功率不平滑,制氢质量受到影
响,主控制器70获取到公共直流母线电压变化量和/或不控整流单元40输入端的波动功率,
11
CN 115976571 A
说明书
8/9页
即储能调节信号,控制飞轮储能装置80的电机转矩,使得飞轮转速降低,进而向公共直流母
线吸收有功功率或无功功率,使公共直流母线电压以及不控整流单元40输入端的功率均为
稳定的值,确保制氢装置10的制氢质量。如此,通过在制氢控制系统中设置飞轮储能装置,
可以平抑新能源的功率波动、提高制氢质量。
[0082] 可选的,主控制器70还用于获取上级调度指令,并根据上级调度指令,调节飞轮储
能装置80的输入/输出功率。
[0083] 其中,上级调度指令可以理解为人为设定或根据氢气需求量进行设定,以达到控
制制氢量多少的目的。
[0084] 具体的,控制中心接收到上级调度指令时,通过飞轮储能装置与制氢电源控制进
行需求侧响应,减小或增加制氢功率、飞轮储能装置80吸收或输出功率,实现对公共直流母
线和交流电网的功率进行响应。其中,公共直流母线或交流电网变化量△PG、制氢功率变化
量△Pe、飞轮储能装置80输出功率变化量△PFL之间的关系为:△PG=> Pe+ APFL。
[0085] 示例性的,当用户需要大量的氢气时,此时交流电网为制氢装置10提供的功率不
能满足该需求,用户向主控制器70发送相应的上级调度指令,主控制器70获取到该指令后,
控制飞轮储能装置80的电机转矩,使得飞轮转速升高,此时飞轮储能装置80向公共直流母
线输出有功功率,相应的制氢装置10接收到的功率变大,进而制取氢气的量变大。相反,可
以控制飞轮储能装置80的电机转矩,使得飞轮转速降低,以减小向公共直流母线输出或吸
收的有功功率或无功功率。
[0086] 可选的,主控制器70还用于在飞轮储能装置80投入运行前,获取飞轮储能装置80
的储能能量,根据储能能量,控制飞轮储能装置80从公共直流母线吸收能量,以使飞轮储能
装置80的储能能量达到预设储能能量。其中,飞轮储能装置80的储能模块包括超级电容器
以及其他功率型的储能系统。
[0087] 具体的,由于飞轮储能装置80需要根据公共直流母线电压变化量、不控整流单元
40输入端的波动功率,向交流电网吸收/输出有功功率或无功功率,因此飞轮储能装置80需
要具有一定的储能能量。在投入运行前,主控制器70可获取飞轮储能装置80的储能能量,根
据储能能量,控制飞轮储能装置80从公共直流母线吸收能量或输出功率,使得飞轮转速升
高,达到飞轮储能额定能量的50%~90%的预设储能能量,此时飞轮储能装置80既有一定
的储能能量向公共直流母线输出有功功率或无功功率,又有一定的容量从公共直流母线吸
收有功功率或无功功率。
[0088] 可选的,参考图3,制氢控制系统还包括滤波单元90;滤波单元90与交流电网电连
接;滤波单元90用于对交流电网的交流信号进行滤波。
[0089] 具体的,由于交流电网供电时会产生一定的波动信号,产生噪声干扰,同时制氢装
置10在工作时也会产生一定的噪声干扰,滤波单元90可以滤除特定频率的谐波,大大衰减
随交流电传入的噪声干扰,同时又能有效地阻止制氢装置10产生的噪声干扰进入交流电网
影响其它电子设备。
[0090]
本发明实施例提供的一种具有需求侧响应的制氢控制系统,通过在制氢装置启动
时,不控整流单元将交流电网的交流信号转换为直流信号,以满足制氢装置的供电需求,然
后制氢电源控制器控制DC/DC电压转换单元将直流信号转换为具有恒定电压的供电电源,
并提供至制氢装置,进而控制制氢装置稳定且快速地启动;而在制氢装置启动后,且其温度
12
CN 115976571 A
说明书
9/9页
达到预设温度时,制氢电源控制器控制DC/DC电压转换单元将交流电网的交流信号转换为
具有恒定电流的供电电源,以控制制氢装置进行制氢,同时设置了飞轮储能装置、制氢辅助
装置和制氢辅助控制器以及滤波单元等,在实现高质量且稳定地制氢的同时可以平抑新能
源的功率波动,同时有助于公共直流母线的稳定,紧急时为电网提供需求侧响应。
[0091] 应该理解,可以使用上面所示的各种形式的流程,重新排序、增加或删除步骤。例
如,本发明中记载的各步骤可以并行地执行也可以顺序地执行也可以不同的次序执行,只
要能够实现本发明的技术方案所期望的结果,本文在此不进行限制。
[0092] 上述具体实施方式,并不构成对本发明保护范围的限制。本领域技术人员应该明
白的是,根据设计要求和其他因素,可以进行各种修改、组合、子组合和替代。任何在本发明
的精神和原则之內所作的修改、等同替换和改进等,均应包含在本发明保护范围之内。
13
CN 115976571 A
说明书附图
30
制氢电源
控制器
10
40
41
公共直流
母线
交流电网
114
DC/DC
电压转
换
单元
41
图1
20
23
al
本
21
21
bl
40
本
A
cl
a
Δ
Δ
太
B
C
b
C
图2
14
21
22
22
22
制氢装
置
1/2页
CN 115976571 A
说明书附图
2/2页
70
上级调
度指令
60
主控制器
制氢辅助
控制器
40
41
交流电
网
制氢电源
控制器
制氢辅助
装置
50
30
公共直
流母线
DC/DC
降压
单元
制氢
装置
20
10
滤波单元
90
飞轮储能
系统
80
图3
15
