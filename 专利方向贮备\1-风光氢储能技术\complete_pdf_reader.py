#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整PDF内容读取器
读取博士论文PDF的全部内容
"""

import sys
import subprocess
from pathlib import Path

def install_required_packages():
    """安装必需的包"""
    packages = ['PyMuPDF', 'pymupdf']
    
    for package in packages:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package, '--quiet'])
            print(f"✅ 成功安装 {package}")
            break
        except:
            print(f"⚠️ 安装 {package} 失败，尝试下一个...")
            continue

def extract_complete_pdf_content(pdf_path):
    """提取PDF完整内容"""
    try:
        # 尝试导入fitz (PyMuPDF)
        try:
            import fitz
        except ImportError:
            print("📦 正在安装PyMuPDF...")
            install_required_packages()
            import fitz
        
        print(f"🔍 开始读取PDF: {pdf_path}")
        
        # 打开PDF文档
        doc = fitz.open(pdf_path)
        total_pages = doc.page_count
        print(f"📚 总页数: {total_pages}")
        
        all_text = ""
        successful_pages = 0
        
        # 逐页提取内容
        for page_num in range(total_pages):
            try:
                page = doc[page_num]
                page_text = page.get_text()
                
                if page_text and len(page_text.strip()) > 10:  # 过滤掉空白页
                    all_text += f"\n{'='*80}\n"
                    all_text += f"第 {page_num + 1} 页 / 共 {total_pages} 页\n"
                    all_text += f"{'='*80}\n"
                    all_text += page_text
                    all_text += "\n"
                    successful_pages += 1
                
                # 每10页显示一次进度
                if (page_num + 1) % 10 == 0:
                    print(f"📖 进度: {page_num + 1}/{total_pages} 页")
            
            except Exception as e:
                print(f"⚠️ 第{page_num + 1}页提取失败: {e}")
                continue
        
        doc.close()
        
        print(f"✅ 提取完成！成功读取 {successful_pages}/{total_pages} 页")
        print(f"📝 总字符数: {len(all_text):,}")
        
        return all_text, successful_pages, total_pages
        
    except Exception as e:
        print(f"❌ PDF读取失败: {e}")
        return None, 0, 0

def save_content_to_file(content, output_path):
    """保存内容到文件"""
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("风光氢综合能源系统优化配置与协调控制策略研究[博士论文] - 完整内容\n")
            f.write("作者: 孔令国\n")
            f.write("学校: 华北电力大学\n")
            f.write("年份: 2017年\n")
            f.write("="*100 + "\n\n")
            f.write(content)
        
        print(f"💾 完整内容已保存到: {output_path.name}")
        return True
    except Exception as e:
        print(f"❌ 保存失败: {e}")
        return False

def analyze_content(content):
    """分析文本内容"""
    if not content:
        return {}
    
    analysis = {
        'total_chars': len(content),
        'total_lines': content.count('\n'),
        'pages_extracted': content.count('第') if '第' in content else 0,
    }
    
    # 查找技术关键词
    keywords = [
        '风光氢', '储能系统', '协调控制', '优化配置', '电解槽', '燃料电池',
        '化学反应优化算法', 'ICROA', 'HOMER', 'PSCAD', 'EMTDC',
        '直驱永磁同步', '质子交换膜', '碱式电解', '弃风弃光',
        '功率波动', '模型预测控制', 'MPC', '多目标优化',
        '粒子群算法', '遗传算法', '仿真验证', '物理实验'
    ]
    
    found_keywords = []
    for keyword in keywords:
        if keyword in content:
            count = content.count(keyword)
            found_keywords.append((keyword, count))
    
    analysis['keywords'] = sorted(found_keywords, key=lambda x: x[1], reverse=True)
    
    return analysis

def main():
    """主函数"""
    print("🚀 完整PDF内容读取器")
    print("="*80)
    
    # 确定PDF文件路径
    pdf_path = Path("01-中文论文/风光氢综合能源系统优化配置与协调控制策略研究[博士论文].PDF")
    
    if not pdf_path.exists():
        print(f"❌ 文件不存在: {pdf_path}")
        print("📁 当前目录文件:")
        for file in Path(".").iterdir():
            if file.is_file():
                print(f"   {file.name}")
        return
    
    print(f"📄 目标文件: {pdf_path.name}")
    print(f"📏 文件大小: {pdf_path.stat().st_size / 1024 / 1024:.2f} MB")
    print()
    
    # 提取完整内容
    content, successful_pages, total_pages = extract_complete_pdf_content(pdf_path)
    
    if content:
        # 保存完整内容
        output_path = Path("博士论文_完整内容.txt")
        if save_content_to_file(content, output_path):
            
            # 分析内容
            print("\n📊 内容分析:")
            analysis = analyze_content(content)
            
            print(f"   总字符数: {analysis['total_chars']:,}")
            print(f"   总行数: {analysis['total_lines']:,}")
            print(f"   成功提取页数: {successful_pages}/{total_pages}")
            print(f"   提取成功率: {successful_pages/total_pages*100:.1f}%")
            
            if analysis['keywords']:
                print("\n🔑 主要技术关键词:")
                for keyword, count in analysis['keywords'][:10]:
                    print(f"   {keyword}: {count} 次")
            
            print(f"\n✅ 完整内容提取成功！文件保存为: {output_path.name}")
            
            # 创建内容摘要
            create_content_summary(content, analysis)
        
    else:
        print("❌ 内容提取失败")

def create_content_summary(content, analysis):
    """创建内容摘要"""
    try:
        summary_path = Path("博士论文_内容摘要.txt")
        
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write("博士论文内容摘要\n")
            f.write("="*60 + "\n\n")
            
            f.write("📊 统计信息:\n")
            f.write(f"总字符数: {analysis['total_chars']:,}\n")
            f.write(f"总行数: {analysis['total_lines']:,}\n\n")
            
            f.write("🔑 主要技术关键词:\n")
            for keyword, count in analysis['keywords'][:15]:
                f.write(f"{keyword}: {count} 次\n")
            f.write("\n")
            
            # 提取章节标题
            f.write("📚 主要章节:\n")
            lines = content.split('\n')
            chapters = []
            for line in lines:
                line = line.strip()
                if ('第' in line and '章' in line) or ('Chapter' in line):
                    if len(line) < 100:  # 避免过长的行
                        chapters.append(line)
            
            for chapter in chapters[:20]:  # 只显示前20个章节
                f.write(f"- {chapter}\n")
            
            f.write(f"\n✅ 摘要已保存到: {summary_path.name}\n")
        
        print(f"📋 内容摘要已保存到: {summary_path.name}")
        
    except Exception as e:
        print(f"⚠️ 创建摘要失败: {e}")

if __name__ == "__main__":
    try:
        main()
        print("\n🎉 PDF完整内容读取任务完成！")
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序出现错误: {e}")
        import traceback
        traceback.print_exc()
