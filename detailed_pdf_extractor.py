#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细PDF内容提取器 - 专门用于提取专利文档的详细内容
"""

import sys
from pathlib import Path

def extract_detailed_content(pdf_path):
    """提取PDF的详细内容"""
    try:
        import fitz  # pymupdf
        doc = fitz.open(pdf_path)
        
        full_text = ""
        for page_num in range(len(doc)):
            page = doc[page_num]
            text = page.get_text()
            full_text += f"\n=== 第{page_num+1}页 ===\n"
            full_text += text
        
        doc.close()
        
        # 提取关键技术部分
        sections = extract_key_sections(full_text)
        return sections
        
    except Exception as e:
        return f"提取错误: {e}"

def extract_key_sections(text):
    """提取关键技术章节"""
    result = []
    
    # 查找技术领域
    tech_field = extract_section(text, "技术领域", ["背景技术", "发明内容"])
    if tech_field:
        result.append(f"=== 技术领域 ===\n{tech_field}\n")
    
    # 查找背景技术
    background = extract_section(text, "背景技术", ["发明内容", "技术问题"])
    if background:
        result.append(f"=== 背景技术 ===\n{background}\n")
    
    # 查找发明内容
    invention = extract_section(text, "发明内容", ["附图说明", "具体实施方式"])
    if invention:
        result.append(f"=== 发明内容 ===\n{invention}\n")
    
    # 查找具体实施方式
    implementation = extract_section(text, "具体实施方式", ["权利要求", "说明书"])
    if implementation:
        result.append(f"=== 具体实施方式 ===\n{implementation}\n")
    
    # 如果没有找到标准章节，返回前2000字符
    if not result:
        result.append(f"=== 文档内容预览 ===\n{text[:2000]}...\n")
    
    return '\n'.join(result)

def extract_section(text, start_marker, end_markers):
    """提取指定章节的内容"""
    text_lower = text.lower()
    start_pos = text_lower.find(start_marker.lower())
    
    if start_pos == -1:
        return None
    
    # 查找结束位置
    end_pos = len(text)
    for end_marker in end_markers:
        pos = text_lower.find(end_marker.lower(), start_pos + len(start_marker))
        if pos != -1 and pos < end_pos:
            end_pos = pos
    
    section_text = text[start_pos:end_pos].strip()
    
    # 清理章节标题
    section_text = section_text.replace(start_marker, '').strip()
    
    if len(section_text) > 50:
        return section_text
    
    return None

if __name__ == "__main__":
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
        if Path(pdf_path).exists():
            result = extract_detailed_content(pdf_path)
            print(result)
        else:
            print("PDF文件不存在")
    else:
        print("请提供PDF文件路径")