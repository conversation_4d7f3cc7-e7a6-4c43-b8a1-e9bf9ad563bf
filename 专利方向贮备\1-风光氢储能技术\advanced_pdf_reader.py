#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级PDF内容读取器
尝试多种方法提取PDF文本内容
"""

import os
import sys
import re
import subprocess
from pathlib import Path

def install_package(package_name):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package_name])
        return True
    except:
        return False

def try_import_libraries():
    """尝试导入PDF处理库"""
    libraries = {}
    
    # 尝试导入PyPDF2
    try:
        import PyPDF2
        libraries['PyPDF2'] = PyPDF2
        print("✅ PyPDF2 可用")
    except ImportError:
        print("⚠️ PyPDF2 未安装，尝试安装...")
        if install_package('PyPDF2'):
            try:
                import PyPDF2
                libraries['PyPDF2'] = PyPDF2
                print("✅ PyPDF2 安装成功")
            except:
                print("❌ PyPDF2 安装失败")
        else:
            print("❌ PyPDF2 安装失败")
    
    # 尝试导入pdfplumber
    try:
        import pdfplumber
        libraries['pdfplumber'] = pdfplumber
        print("✅ pdfplumber 可用")
    except ImportError:
        print("⚠️ pdfplumber 未安装，尝试安装...")
        if install_package('pdfplumber'):
            try:
                import pdfplumber
                libraries['pdfplumber'] = pdfplumber
                print("✅ pdfplumber 安装成功")
            except:
                print("❌ pdfplumber 安装失败")
        else:
            print("❌ pdfplumber 安装失败")
    
    # 尝试导入pymupdf
    try:
        import fitz  # PyMuPDF
        libraries['PyMuPDF'] = fitz
        print("✅ PyMuPDF 可用")
    except ImportError:
        print("⚠️ PyMuPDF 未安装，尝试安装...")
        if install_package('PyMuPDF'):
            try:
                import fitz
                libraries['PyMuPDF'] = fitz
                print("✅ PyMuPDF 安装成功")
            except:
                print("❌ PyMuPDF 安装失败")
        else:
            print("❌ PyMuPDF 安装失败")
    
    return libraries

def extract_with_pypdf2(pdf_path, PyPDF2):
    """使用PyPDF2提取文本"""
    try:
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            text = ""
            
            # 获取元数据
            metadata = {}
            if reader.metadata:
                metadata = {
                    'title': reader.metadata.get('/Title', ''),
                    'author': reader.metadata.get('/Author', ''),
                    'subject': reader.metadata.get('/Subject', ''),
                    'pages': len(reader.pages)
                }
            
            # 提取前5页文本
            for i, page in enumerate(reader.pages[:5]):
                try:
                    page_text = page.extract_text()
                    if page_text:
                        text += f"\\n=== 第{i+1}页 ===\\n"
                        text += page_text[:1000]  # 每页限制1000字符
                except:
                    continue
            
            return {
                'method': 'PyPDF2',
                'success': True,
                'text': text,
                'metadata': metadata
            }
    except Exception as e:
        return {
            'method': 'PyPDF2',
            'success': False,
            'error': str(e)
        }

def extract_with_pdfplumber(pdf_path, pdfplumber):
    """使用pdfplumber提取文本"""
    try:
        with pdfplumber.open(pdf_path) as pdf:
            text = ""
            
            # 获取基本信息
            metadata = {
                'pages': len(pdf.pages),
                'method': 'pdfplumber'
            }
            
            # 提取前5页文本
            for i, page in enumerate(pdf.pages[:5]):
                try:
                    page_text = page.extract_text()
                    if page_text:
                        text += f"\\n=== 第{i+1}页 ===\\n"
                        text += page_text[:1000]  # 每页限制1000字符
                except:
                    continue
            
            return {
                'method': 'pdfplumber',
                'success': True,
                'text': text,
                'metadata': metadata
            }
    except Exception as e:
        return {
            'method': 'pdfplumber',
            'success': False,
            'error': str(e)
        }

def extract_with_pymupdf(pdf_path, fitz):
    """使用PyMuPDF提取文本"""
    try:
        doc = fitz.open(pdf_path)
        text = ""
        
        # 获取基本信息
        metadata = {
            'pages': doc.page_count,
            'title': doc.metadata.get('title', ''),
            'author': doc.metadata.get('author', ''),
            'method': 'PyMuPDF'
        }
        
        # 提取前5页文本
        for i in range(min(5, doc.page_count)):
            try:
                page = doc[i]
                page_text = page.get_text()
                if page_text:
                    text += f"\\n=== 第{i+1}页 ===\\n"
                    text += page_text[:1000]  # 每页限制1000字符
            except:
                continue
        
        doc.close()
        
        return {
            'method': 'PyMuPDF',
            'success': True,
            'text': text,
            'metadata': metadata
        }
    except Exception as e:
        return {
            'method': 'PyMuPDF',
            'success': False,
            'error': str(e)
        }

def extract_pdf_content(pdf_path, libraries):
    """使用多种方法提取PDF内容"""
    results = []
    
    # 尝试所有可用的方法
    if 'PyMuPDF' in libraries:
        result = extract_with_pymupdf(pdf_path, libraries['PyMuPDF'])
        results.append(result)
        if result['success'] and len(result['text']) > 100:
            return result  # PyMuPDF通常效果最好，如果成功就直接返回
    
    if 'pdfplumber' in libraries:
        result = extract_with_pdfplumber(pdf_path, libraries['pdfplumber'])
        results.append(result)
        if result['success'] and len(result['text']) > 100:
            return result
    
    if 'PyPDF2' in libraries:
        result = extract_with_pypdf2(pdf_path, libraries['PyPDF2'])
        results.append(result)
        if result['success'] and len(result['text']) > 100:
            return result
    
    # 如果所有方法都失败或提取内容太少，返回最好的结果
    successful_results = [r for r in results if r['success']]
    if successful_results:
        return max(successful_results, key=lambda x: len(x.get('text', '')))
    
    return {
        'method': 'None',
        'success': False,
        'error': '所有提取方法都失败',
        'attempted_methods': [r['method'] for r in results]
    }

def analyze_content(text, filename):
    """分析提取的文本内容"""
    analysis = {
        'word_count': len(text),
        'has_chinese': bool(re.search(r'[\\u4e00-\\u9fff]', text)),
        'has_english': bool(re.search(r'[a-zA-Z]', text)),
        'keywords_found': [],
        'technical_terms': []
    }
    
    # 查找技术关键词
    chinese_keywords = [
        '风光氢', '储能', '制氢', '电解', '控制', '优化', '系统',
        '模型预测', 'MPC', '功率', '能源', '可再生', '数字孪生',
        'PID', '氢气', '太阳能', '风能', '光伏', '燃料电池'
    ]
    
    english_keywords = [
        'hydrogen', 'wind', 'solar', 'energy', 'storage', 'control',
        'optimization', 'system', 'renewable', 'electrolysis', 'PID',
        'power', 'fuel cell', 'photovoltaic', 'digital twin'
    ]
    
    # 检查中文关键词
    for keyword in chinese_keywords:
        if keyword in text:
            analysis['keywords_found'].append(keyword)
    
    # 检查英文关键词  
    text_lower = text.lower()
    for keyword in english_keywords:
        if keyword in text_lower:
            analysis['keywords_found'].append(keyword)
    
    # 提取可能的技术术语（连续的大写字母组合）
    tech_terms = re.findall(r'\\b[A-Z]{2,}\\b', text)
    analysis['technical_terms'] = list(set(tech_terms))
    
    return analysis

def process_all_pdfs():
    """处理所有PDF文件"""
    print("🚀 高级PDF内容读取器")
    print("=" * 60)
    
    # 导入库
    libraries = try_import_libraries()
    if not libraries:
        print("❌ 无法导入任何PDF处理库，退出程序")
        return
    
    print(f"✅ 成功导入 {len(libraries)} 个PDF处理库")
    print()
    
    # 查找PDF文件
    current_dir = Path(__file__).parent
    pdf_files = []
    for pattern in ['*.pdf', '*.PDF']:
        pdf_files.extend(current_dir.rglob(pattern))
    
    if not pdf_files:
        print("❌ 未找到PDF文件")
        return
    
    print(f"📚 找到 {len(pdf_files)} 个PDF文件")
    print()
    
    results = []
    
    for i, pdf_path in enumerate(pdf_files, 1):
        print(f"📖 处理文件 {i}/{len(pdf_files)}: {pdf_path.name}")
        print(f"   路径: {pdf_path.parent.name}/")
        print(f"   大小: {pdf_path.stat().st_size / 1024 / 1024:.2f} MB")
        
        # 提取内容
        extraction_result = extract_pdf_content(pdf_path, libraries)
        
        if extraction_result['success']:
            print(f"   ✅ 成功提取 ({extraction_result['method']})")
            print(f"   📝 文本长度: {len(extraction_result['text'])} 字符")
            
            # 分析内容
            content_analysis = analyze_content(extraction_result['text'], pdf_path.name)
            print(f"   🔍 包含中文: {'是' if content_analysis['has_chinese'] else '否'}")
            print(f"   🔍 包含英文: {'是' if content_analysis['has_english'] else '否'}")
            
            if content_analysis['keywords_found']:
                print(f"   🎯 关键词: {', '.join(content_analysis['keywords_found'][:5])}")
            
            # 显示内容预览
            preview = extraction_result['text'][:300]
            if len(extraction_result['text']) > 300:
                preview += "..."
            print(f"   📄 内容预览: {preview}")
            
        else:
            print(f"   ❌ 提取失败: {extraction_result.get('error', '未知错误')}")
        
        print()
        
        # 保存结果
        result = {
            'file_path': str(pdf_path),
            'file_name': pdf_path.name,
            'extraction_result': extraction_result
        }
        
        if extraction_result['success']:
            result['content_analysis'] = content_analysis
        
        results.append(result)
    
    # 生成详细报告
    report_path = current_dir / "详细PDF内容报告.txt"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("详细PDF内容提取报告\\n")
        f.write("=" * 80 + "\\n\\n")
        f.write(f"处理时间: {os.popen('echo %date% %time%').read().strip()}\\n")
        f.write(f"处理文件数: {len(results)}\\n")
        f.write(f"使用库: {', '.join(libraries.keys())}\\n\\n")
        
        for i, result in enumerate(results, 1):
            f.write(f"\\n{'-'*60}\\n")
            f.write(f"文件 {i}: {result['file_name']}\\n")
            f.write(f"路径: {result['file_path']}\\n")
            
            if result['extraction_result']['success']:
                f.write(f"提取方法: {result['extraction_result']['method']}\\n")
                f.write(f"文本长度: {len(result['extraction_result']['text'])} 字符\\n")
                
                if 'metadata' in result['extraction_result']:
                    metadata = result['extraction_result']['metadata']
                    if 'pages' in metadata:
                        f.write(f"页数: {metadata['pages']}\\n")
                    if 'title' in metadata and metadata['title']:
                        f.write(f"标题: {metadata['title']}\\n")
                    if 'author' in metadata and metadata['author']:
                        f.write(f"作者: {metadata['author']}\\n")
                
                if 'content_analysis' in result:
                    analysis = result['content_analysis']
                    f.write(f"关键词: {', '.join(analysis['keywords_found'])}\\n")
                    f.write(f"技术术语: {', '.join(analysis['technical_terms'][:10])}\\n")
                
                f.write("\\n完整内容:\\n")
                f.write("-" * 40 + "\\n")
                f.write(result['extraction_result']['text'])
                f.write("\\n" + "-" * 40 + "\\n")
            else:
                f.write(f"提取失败: {result['extraction_result']['error']}\\n")
    
    print(f"📄 详细报告已保存: {report_path.name}")
    
    # 总结
    successful = len([r for r in results if r['extraction_result']['success']])
    print(f"\\n📊 处理总结:")
    print(f"   成功: {successful}/{len(results)} 个文件")
    print(f"   使用库: {', '.join(libraries.keys())}")
    
    return results

if __name__ == "__main__":
    try:
        results = process_all_pdfs()
        print("\\n🎉 PDF内容提取完成！")
    except KeyboardInterrupt:
        print("\\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\\n❌ 程序出现错误: {e}")
        import traceback
        traceback.print_exc()
