#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word文档阅读器 - 读取当前目录及子目录中的所有Word文档
使用方法: python word_reader.py
"""

import docx2txt
from pathlib import Path
import sys

def read_word_document(file_path, max_length=2000):
    """读取单个Word文档"""
    try:
        content = docx2txt.process(file_path)
        if len(content) > max_length:
            return content[:max_length] + "\n\n[内容过长，已截断...]"
        return content
    except Exception as e:
        return f"读取错误: {e}"

def main():
    if len(sys.argv) > 1:
        # 读取指定文件
        file_path = sys.argv[1]
        if Path(file_path).exists() and file_path.endswith('.docx'):
            content = read_word_document(file_path, max_length=5000)
            print(f"=== {Path(file_path).name} ===")
            print(content)
        else:
            print("文件不存在或不是.docx格式")
    else:
        # 扫描当前目录
        docx_files = list(Path('.').rglob('*.docx'))
        print(f"找到 {len(docx_files)} 个.docx文件\n")
        
        for i, file_path in enumerate(docx_files, 1):
            print(f"{'='*60}")
            print(f"📄 文档 {i}: {file_path}")
            print(f"{'='*60}")
            content = read_word_document(file_path)
            print(content)
            print()

if __name__ == "__main__":
    main() 