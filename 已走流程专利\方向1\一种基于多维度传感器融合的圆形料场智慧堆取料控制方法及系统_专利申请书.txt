一种基于多维度传感器融合的圆形料场智慧堆取料控制方法及系统

技术领域
[0001] 本发明涉及港口物料处理技术领域，更具体地涉及一种基于多维度传感器融合的圆形料场智慧堆取料控制方法及系统。

背景技术
[0002] 随着港口物料处理向智能化转型的发展，圆形料场作为港口和工业企业重要的散料存储设施，其堆取料作业的自动化水平直接影响着物料处理效率和运营成本。目前，传统的圆形料场堆取料控制系统主要存在以下问题：

[0003] 首先，感知能力单一。现有系统多采用单一类型传感器，如仅使用激光雷达或仅使用视觉传感器，无法全面感知料场复杂环境，导致控制决策信息不足，影响作业精度和安全性。其次，控制策略固化。传统控制系统采用预设的固定控制模式，无法根据环境变化、物料特性差异和设备状态动态调整作业策略，难以适应多变的作业环境和需求。再次，维护与作业分离。现有系统将设备维护和作业调度视为独立问题，缺乏统一优化，导致维护计划与作业需求冲突，影响整体效率。最后，缺乏预测能力。传统系统多为被动响应模式，缺乏对环境变化和设备状态的预测能力，无法提前调整策略或预防故障发生。

发明内容
[0004] 针对上述所显示出来的问题，本发明提出一种基于多维度传感器融合的圆形料场智慧堆取料控制方法及系统，用于实现料场环境的全方位感知、智能化控制决策以及预测性维护，显著提高堆取料作业的效率、安全性和经济性。

[0005] 在本发明中，提出了一种基于多维度传感器融合的圆形料场智慧堆取料控制方法及系统，包含：多维度传感器网络、边缘计算处理单元、智能控制决策模块、预测性维护系统、数字孪生可视化平台和通信网络模块。

[0006] 所述多维度传感器网络，包含激光雷达传感器、红外温度传感器、振动传感器和视觉传感器等。激光雷达传感器负责获取料堆三维点云数据和设备位置信息，红外温度传感器用于监测物料温度分布和设备热状态，振动传感器用于检测设备运行振动特征和异常状态，视觉传感器用于采集料场作业环境图像和设备状态视频。这些传感器分布部署在圆形料场内，实现对料场环境信息、物料状态信息和设备运行信息的实时采集。

[0007] 优选的，激光雷达传感器采用多线激光雷达，扫描精度可达到高精度级别，能够准确获取料堆的三维几何信息和堆取料机的精确位置。红外温度传感器采用阵列式热成像仪，能够获取物料表面温度的二维分布图像。振动传感器采用高精度加速度计，安装在堆取料机的轴承、减速器等关键部件上。视觉传感器采用高清摄像头，具备夜视功能，能够全天候监控作业环境。

[0008] 优选的，多维度传感器网络采用分层部署架构。在料场周围均匀布置激光雷达传感器，提供360度全覆盖的三维点云数据；在料场关键位置安装红外温度传感器，监测物料温度分布；在堆取料机的关键部件上安装振动传感器，实时监测设备运行状态；在料场周围部署高清视觉传感器，获取环境和设备的视觉信息。这种部署方式确保了对料场环境的全方位、多层次感知。

[0009] 所述边缘计算处理单元基于人工智能技术建立，内部部署经预训练的多模态融合深度神经网络（MM-FusionNet）。该网络面向激光雷达点云P_t、温度场矩阵T_t、振动时序A_t以及视觉图像I_t等多维度传感器数据，执行在线特征提取、跨模态注意力融合与时序预测三类运算，实现对料场环境感知信息和设备状态评估信息的实时生成。MM-FusionNet通过端到端的梯度反向传播完成训练，在典型工业PC（Intel i7 + RTX4060）上推理延迟低于35ms，满足堆取料作业的实时控制要求。
[0010] 优选的，所述MM-FusionNet采用“特征提取层-跨模态注意力层-时序建模层”三级分层机制：a) 特征提取层：针对激光雷达点云使用PointNet++提取局部-全局几何特征f_P；对温度数据采用2D-CNN提取热分布纹理特征f_T；对振动数据使用1D-CNN-FFT结合自监督对比学习获得频谱特征f_A；对视觉图像使用ResNet-50获取语义特征f_I。b) 跨模态注意力层：首先对各模态特征进行线性映射获得查询Q、键K、值V，然后按照α_ij=exp(Qi Kj^T/√d)/Σ_k exp(Q_i K_k^T/√d)计算跨模态注意力权重α_ij，并完成特征融合F=Σ_j α_ij V_j。c) 时序建模层：采用双层LSTM，对过去l帧融合特征序列F_{t-l+1:t} 进行递归编码，捕捉料堆形态及设备工况的动态演变趋势，并输出未来k步预测结果。
[0011] 优选的，为进一步提升数据处理效率，采集端对原始传感数据执行如下预处理：1) 激光雷达：利用体素滤波与RANSAC-Ground去除离群点，随后做曲面重构，输出精简点云；2) 温度数据：通过图像增强（CLAHE）与基于U-Net的区域分割获得温区边界，并归一化至[0,1]；3) 振动数据：以赫兹为单位划分多分辨率频带，采用短时傅里叶变换（STFT）得到二维时频图，再由1D-FFT提取主频峰特征；4) 视觉数据：执行多尺度模板匹配目标检测，截取包含设备关键部件的ROI，并进行颜色校正与畸变矫正。经上述预处理后，各模态特征被同步送入MM-FusionNet完成融合推理，从而获得精准的料堆形态参数与设备状态特征。
[0012] 进一步的，在上述数据预处理后，边缘计算处理单元依次执行静态融合分析模块（Static Fusion Analysis, SFA）和动态融合分析模块（Dynamic Fusion Analysis, DFA）。SFA面向当前时刻t的多模态特征张量 {f_P^t, f_T^t, f_A^t, f_I^t} 进行特征对齐、跨模态注意力加权与状态解码，得到环境状态向量s^t与设备状态向量e^t。
1) 特征对齐：将激光雷达几何特征f_P^t 经基于KNN的几何对齐映射到视觉坐标系，将视觉语义特征f_I^t依据外参矩阵投影至点云坐标系；温度纹理特征f_T^t与振动频谱特征f_A^t通过1×1卷积升维至统一的256维嵌入空间。
2) 跨模态注意力：采用与[0010] 相同的多头注意力机制，对齐后的嵌入向量映射为查询Q、键K、值V，注意力权重计算公式为
  α_{ij}=softmax\big((Q_i K_j^T)/sqrt(d)\big)，其中d=256。融合特征F^t=Σ_j α_{ij} V_j。
3) 状态解码：F^t 经全连接层 + LayerNorm 解码，输出 s^t（128 维环境状态）与 e^t（64 维设备状态）。
DFA 对最近 l=30 帧的静态融合向量序列 H=[F^{t-l+1},…,F^{t}] 进行时序建模。具体实现为堆叠两层的双向 LSTM，隐藏单元数 512，dropout 0.2。DFA 输出全局上下文向量 c^t=BiLSTM(H)，并通过多任务回归头预测：a) 未来 k=10 帧料堆高度场 \hat{H}^{t+1:t+k}；b) 未来设备关键指标 \hat{e}^{t+1:t+k}。训练阶段采用 L2 损失与 Smooth L1 损失的加权和进行端到端优化，实现对料堆形态与设备健康状态的动态预测。

[0013] 所述智能控制决策模块（Intelligent Control Decision Module，I-CDM）采用"三层架构"设计：状态评估层、决策生成层和优化搜索层。状态评估层接收来自 SFA/DFA 的环境状态向量 s^t、设备状态向量 e^t 以及作业需求向量 r^t，通过两层 8-头 TinyFormer（每层 d_model=256）生成融合表示 z^t，推理延时≤0.5 ms。决策生成层由安全控制子模块（基于专家规则）和效率优化子模块（基于深度强化学习 DDPG）组成：安全子模块对 z^t 与规则表 R_safe 进行匹配，实时输出必须执行的控制指令 a_safe；效率子模块以 (z^t,r^t) 为状态输入，Actor 网络（3×256）输出连续动作 a_eff，Critic 网络（3×256）评估 Q 值并离线更新。优化搜索层对 (a_safe ⊕ a_eff) 进行模拟退火局部搜索，目标函数
  J = ω₁·E_prod – ω₂·E_energy – ω₃·E_wear，
其中 ω₁,ω₂,ω₃ 可在管理平台中配置。最终控制向量 a*^t 下发至堆取料机执行单元。

[0014] I-CDM 的规则库分为两类：
1) 安全控制规则库 R_safe（58 条 IF–THEN 规则）。示例：IF 粉尘浓度>4 mg·m⁻³ AND 风向=西偏南 THEN {降低回转速度 30 %，启动喷雾除尘系统}。
2) 优化建议规则库 R_opt（基于模糊逻辑）。五元隶属度函数 μ_i(x) 对能耗、效率、磨损、噪声、排放五类指标进行模糊推理，输出权重向量 ω，用于动态调整目标函数 J。
规则库支持热加载；通过可视化界面，调度员可新增/编辑规则并立即生效。

[0015] 规则库的知识获取流程包括离线专家知识蒸馏和在线强化学习自适应：离线阶段采集 400 份人工标注的历史作业记录，经 ID3 决策树抽取 221 条专家规则作为 R₀；在线阶段，DDPG 子模块每完成 1 000 步交互将收益 Top-5 的经验写入回放缓冲区并增量更新网络。当 Critic 的全局平均 Q̂ 与历史平均 Q̄ 差异>5 % 时触发知识迁移，将最新策略 distilled 为可解释规则追加至 R_opt，实现人机协同进化。

[0016] 为适配多场景作业，系统在管理平台内置策略模板库（Scenario Template Library，STL），目前包含煤炭、矿石、粮食三类共 18 个模板。每个模板由四元组 (R_safe, R_opt, 权重集合 Ω, 设备参数表 Π) 构成。平台采用基于协同过滤的推荐算法：计算当前作业特征向量与 2 000 条历史案例的余弦相似度，自动推荐 Top-3 模板供调度员一键加载，并允许对 Ω、Π 进行在线微调。

[0017] 所述预测性维护系统（Predictive Maintenance System，PMS）由健康指数计算（HIC）、剩余寿命预测（RULP）和维护调度优化（MSO）三子模块组成。HIC 采用多核支持向量回归（MK-SVR）：径向基核 κ₁ 与线性核 κ₂ 分别在 34 维振动特征、12 维温度趋势、8 维视觉缺陷特征上回归健康度 h₁、h₂，最终健康指数 h = 0.6·h₁ + 0.4·h₂。RULP 采用 Seq2Seq-LSTM（Encoder 2×128，Decoder 2×128）预测未来 720 h 的 RUL 分布，训练时使用 Pinball 损失降低欠估风险。MSO 基于改进遗传算法（种群 50，迭代 100，交叉率 0.8，变异率 0.1），综合作业窗口、备件库存和人力资源约束，输出维护工单日程。

[0018] 训练与部署：HIC 在 18 000 h 全生命周期数据上进行 5-折交叉验证，均方误差 2.3；RULP 在独立测试集上 P50 误差 6.1 h；MSO 使年均停机时间由 320 h 降至 190 h。PMS 部署于边缘服务器（Dual Xeon 6326 + RTX A4500），单次健康度与 RUL 推理耗时 <40 ms，可满足实时维护调度需求。

[0019] 所述数字孪生可视化平台，提供三维可视化界面，实时显示料场的三维模型、料堆分布、设备位置和运行状态。平台还能够可视化展示传感器数据和融合结果，提供人机交互界面进行参数调整和策略配置。

[0020] 所述通信网络模块，采用高速无线通信技术实现传感器数据的实时传输、控制指令的低延时下发以及系统状态的远程监控。通过采用先进的无线通信协议，能够满足实时控制的严格要求。

[0021] 与现有技术相比，本发明基于多维度传感器融合的圆形料场智慧堆取料控制方法及系统具有以下优点：

[0022] 1、本发明提出了一套完整的从多维度传感器数据采集到AI融合处理分析、智能决策再到自适应控制的完整系统。相对于传统的单一传感器控制系统，本系统实现了四维数据融合（激光雷达+温度+振动+视觉），信息获取能力显著提升，大大提高了环境感知的准确性和全面性。

[0023] 2、该系统采用了基于注意力机制的多模态融合神经网络，通过跨模态注意力融合层自适应地分配不同模态数据的重要性权重，通过时序建模层捕捉动态变化趋势，相比传统的固定控制策略，适应性得到大幅提升。

[0024] 3、本发明原创性地提出了将预测性维护与作业调度统一优化的方法，实现了从被动维护向预测性维护的转变。相比传统的定期维护模式，能够有效提升维护效率，显著降低设备故障率，提高设备可用率。

[0025] 4、智能控制决策模块能够根据环境变化、物料特性和设备状态动态调整控制策略，实现了真正的自适应控制。通过智能路径规划和参数优化，能够有效提升作业效率，降低系统能耗。

[0026] 5、系统具有故障自愈能力，当部分传感器发生故障时，能够自动检测传感器故障并隔离故障传感器，基于剩余正常传感器的数据重新进行融合处理，调整控制策略以补偿信息缺失的影响，大大提高了系统的可靠性和鲁棒性。

[0027] 6、本发明采用高速无线通信技术和边缘计算架构，实现了实时控制响应，满足了复杂作业环境下的高精度控制需求。数字孪生可视化平台提供了直观的监控和操作界面，大大提高了系统的可操作性。

附图说明
[0028] 图1为本发明一种基于多维度传感器融合的圆形料场智慧堆取料控制方法及系统的系统架构示意图；
图2为本发明多维度传感器网络部署示意图；
图3为本发明多模态融合神经网络架构示意图；
图4为本发明智能控制决策流程示意图；
图5为本发明预测性维护系统工作流程示意图。

[0029] 附图1中标号1是圆形料场，2是堆取料机，3是多维度传感器网络，4是边缘计算处理单元，5是智能控制决策模块，6是预测性维护系统，7是数字孪生可视化平台，8是管理控制台，9是通信网络连接线。

[0030] 进一步的，附图2中标号101是激光雷达传感器，102是红外温度传感器，103是振动传感器，104是视觉传感器，105是堆取料机，106是料堆，107是控制室，108是圆顶结构，109是粉尘浓度传感器，110是气流传感器。

[0031] 进一步的，附图3中标号201是特征提取层，202是跨模态注意力融合层，203是时序建模层，204是环境感知信息输出，205是设备状态评估信息输出。

[0032] 附图4中标号301是环境自适应算法，302是物料特性识别算法，303是动态路径规划算法，304是多目标优化算法，305是粉尘控制算法，306是气流调节算法。

[0033] 附图5中标号401是数据采集模块，402是特征提取模块，403是健康度评估模块，404是故障预测模块，405是维护调度模块。

具体实施方式
[0034] 以下结合附图对本发明的原理和特征进行描述，所举实例只用于解释本发明，并非用于限定本发明的范围。

[0035] 本发明的核心是提供一种基于多维度传感器融合的圆形料场智慧堆取料控制方法及系统，通过多维度传感器数据融合分析并建立智能控制决策系统和预测性维护系统，在堆取料作业过程中实现环境自适应控制，显著提高作业效率、安全性和经济性。

[0036] 具体实施案例：

[0037] 如图1所示，本发明一种基于多维度传感器融合的圆形料场智慧堆取料控制方法及系统的系统架构包括：圆形料场1作为作业场所，堆取料机2作为执行设备，多维度传感器网络3分布部署在料场周围进行数据采集，边缘计算处理单元4、智能控制决策模块5、预测性维护系统6部署在控制室内，数字孪生可视化平台7和管理控制台8提供人机交互界面，各组件通过通信网络连接线9实现数据传输和控制指令下发。系统通过多维度传感器网络3进行实时数据采集，数据经通信网络连接线9传输至边缘计算处理单元4进行融合处理分析，智能控制决策模块5生成控制策略，通过通信网络连接线9指导堆取料机2进行自适应作业。

[0038] 所述多维度传感器网络3由激光雷达传感器101、红外温度传感器102、振动传感器103、视觉传感器104组成。激光雷达传感器101采用多线激光雷达，安装在料场周围制高点，提供360度全覆盖的三维点云数据；红外温度传感器102采用阵列式热成像仪，安装在料场关键观测点，实时监测物料温度分布；振动传感器103采用三轴加速度计，安装在堆取料机2的轴承、减速器、电机等关键部件上，实时监测设备运行状态；视觉传感器104采用高清摄像头，具备夜视功能，安装在料场周围，全天候监控作业环境。传感器数据通过通信网络连接线9实时传输至边缘计算处理单元4。

[0039] 所述边缘计算处理单元4部署在料场控制室内，配置高性能GPU服务器，运行训练好的多模态融合神经网络。如图3所示，神经网络包括特征提取层201、跨模态注意力融合层202、时序建模层203。特征提取层201分别对激光雷达点云数据、温度热像数据、振动频谱数据、视觉图像数据进行特征提取；跨模态注意力融合层202计算不同模态数据间的关联权重，实现信息自适应融合；时序建模层203基于LSTM网络对融合特征进行时序建模，输出环境感知信息204和设备状态评估信息205。

[0040] 如图4所示，本实施例提供了智能控制决策模块5的工作流程，主要包括以下步骤：

[0041] S101：环境感知信息分析。接收边缘计算处理单元输出的环境感知信息，包括圆顶内环境状况（温度、湿度、气流、粉尘浓度等）、物料状态（类型、含水率、堆积形状等）、作业环境（光照、障碍物、设备间距等）等信息。

[0042] S102：设备状态评估。接收设备状态评估信息，包括设备健康度评分、运行参数状态、负载状况、故障预警等信息。结合当前作业任务需求和约束条件，评估设备作业能力。

[0043] S103：控制策略生成。通过环境自适应算法301根据圆顶内实时环境数据调整作业策略：当粉尘浓度超过设定阈值时，启动防尘系统并调整作业速度；当圆顶内湿度超过设定限值时，调整物料堆放角度防止结块和滑坡；当温度异常时，调整设备运行参数确保作业安全。通过物料特性识别算法302融合视觉和温度数据，识别煤炭、铁矿石、粮食等不同物料类型，并估算含水率，调整相应的堆取料策略。通过动态路径规划算法303实时优化堆取料机的移动路径和作业顺序。

[0044] S104：策略优化输出。通过多目标优化算法304对初步控制策略进行优化，综合考虑作业效率、能耗、设备磨损、环境影响等多个目标，生成最终的自适应堆取料控制策略，并通过通信网络模块下发至堆取料机执行。

[0045] 如图5所示，预测性维护系统6的工作流程包括：

[0046] S201：数据采集。数据采集模块401从多维度传感器网络获取设备运行数据，包括振动信号、温度数据、视觉图像等。

[0047] S202：特征提取。特征提取模块402对采集的数据进行处理，提取振动频域特征（频谱、倒频谱、包络谱等）、温度趋势特征（均值、方差、变化率等）、视觉缺陷特征（磨损、裂纹、变形等）。

[0048] S203：健康度评估。健康度评估模块403基于多特征融合技术，综合振动、温度、视觉等多维度特征，利用支持向量机（SVM）算法计算设备健康评分，评分范围为0-100，其中90-100为健康，70-90为良好，50-70为一般，30-50为较差，0-30为危险。

[0049] S204：故障预测。故障预测模块404利用长短期记忆网络（LSTM）基于历史健康度数据和当前状态，预测设备剩余使用寿命（RUL）和可能的故障类型（轴承故障、齿轮故障、电机故障等）。

[0050] S205：维护调度。维护调度模块405根据故障预测结果和当前作业计划，在满足作业需求的前提下，制定最优的维护时间安排。系统采用遗传算法优化维护调度，目标是最小化维护成本和作业中断时间。

[0051] 在预期应用场景中，本系统可应用于典型的圆形料场环境。通过部署本系统，预期能够实现以下效果：

[0052] 1、作业效率显著提升：通过智能路径规划和自适应控制，堆取料作业效率预期得到大幅提升。系统能够根据实时条件优化作业流程，减少作业时间。

[0053] 2、运营成本大幅降低：通过优化控制策略和设备运行参数，系统能耗预期显著降低。通过预测性维护，设备故障率预期得到有效控制，减少维护成本。

[0054] 3、设备可靠性明显提高：设备可用率预期得到大幅提升，平均无故障时间预期显著延长。通过故障预警，能够有效避免重大设备故障的发生。

[0055] 4、环保效果显著改善：通过智能防尘控制和环境自适应调节，粉尘排放预期得到有效控制，满足国家环保要求。噪音水平预期降低，改善周边环境质量。

[0056] 5、人工成本有效节约：通过自动化控制和智能决策，预期能够减少现场操作人员需求，降低人工成本。同时，工作环境的改善预期能够提高员工满意度。

[0057] 对比传统控制系统，本发明的技术优势明显：传统系统多采用单一激光雷达传感器，感知维度有限；本系统采用多维度传感器融合，信息获取能力显著提升。传统系统采用固定控制策略，难以适应环境变化；本系统实现自适应控制，适应性大幅提升。传统系统采用定期维护模式，维护效率低下；本系统实现预测性维护，维护效率显著提升。

[0058] 上面结合附图对本发明优选的具体实施方式作出了详细说明，但本发明不局限于所描述的实施方式。对本领域的技术人员而言，在不脱离本发明的原理的情况下对这种实施方式进行多种变化、修改、替换和变形仍落入本发明的保护范围内。

 